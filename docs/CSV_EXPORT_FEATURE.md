# CSV Export Feature for Daily View

## Overview

The Daily View now supports exporting the current day's tasks to a CSV file. This feature allows users to easily share, analyze, or backup their daily task schedules.

## How to Use

1. Navigate to the Daily View in the calendar section
2. Select the date you want to export
3. Click the "Export CSV" button in the header toolbar (next to the Groups button)
4. The CSV file will be automatically downloaded with the filename format: `daily-view-YYYY-MM-DD.csv`

## CSV File Structure

The exported CSV file contains the following columns:

| Column | Description |
|--------|-------------|
| Task Name | The name/title of the task |
| Type | Task type (e.g., Setup, Catering, Planning) |
| Status | Current status (e.g., Not Started, In Progress, Completed) |
| Start Time | Task start time in HH:MM:SS format |
| End Time | Calculated end time based on duration or deadlines |
| Duration | Task duration in HH:MM:SS format |
| Assignees | Comma-separated list of assigned users (or "Unassigned") |
| Location | Task location |
| Details | Task description/details |
| Soft Deadline | Soft deadline in full date/time format |
| Hard Deadline | Hard deadline in full date/time format |

## Features

- **Automatic Time Calculation**: End times are calculated based on:
  1. Start time + duration (if duration is specified)
  2. Soft deadline (if no duration)
  3. Hard deadline (if no duration or soft deadline)
  4. Default 1-hour duration (if none of the above)

- **Proper CSV Escaping**: Fields containing commas, quotes, or newlines are properly escaped and quoted

- **Multi-assignee Support**: Multiple assignees are joined with commas and properly quoted

- **Internationalization**: Button text and tooltips support both English and Chinese translations

- **Date-specific Export**: Only exports tasks for the currently selected date

## Technical Implementation

The CSV export functionality is implemented directly in the DailyView component with:

- Client-side CSV generation (no server round-trip required)
- Proper CSV formatting with RFC 4180 compliance
- Automatic file download using browser APIs
- Responsive UI integration with existing toolbar

## Translation Keys

The feature uses the following translation keys:

- `calendar.dailyView.exportCSV`: Button text
- `calendar.dailyView.exportCSVTooltip`: Tooltip text

## Browser Compatibility

The feature works in all modern browsers that support:
- Blob API
- URL.createObjectURL()
- HTML5 download attribute

This includes Chrome, Firefox, Safari, and Edge (modern versions).
