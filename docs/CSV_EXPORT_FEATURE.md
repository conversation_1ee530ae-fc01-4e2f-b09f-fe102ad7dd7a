# CSV Export Feature for Daily View

## Overview

The Daily View now supports exporting the current day's tasks to a CSV file. This feature allows users to easily share, analyze, or backup their daily task schedules.

## How to Use

1. Navigate to the Daily View in the calendar section
2. Select the date you want to export
3. Click the "Export CSV" button in the header toolbar (next to the Groups button)
4. The CSV file will be automatically downloaded with the filename format: `daily-view-timeline-YYYY-MM-DD.csv`

## CSV File Structure

The exported CSV file uses a **timeline grid format** that mirrors the Daily View layout:

- **Rows**: Time slots (24 hours from 12:00 AM to 11:00 PM)
- **Columns**: Assignees (including "Unassigned" and group names)
- **Cells**: Task information for what each assignee is doing at that time

### Example Structure:
```
<PERSON>,<PERSON>,<PERSON>,Unassigned,Marketing Team (Group)
12:00 AM,,,Setup venue [Setup] (In Progress) @ Main Hall,
1:00 AM,,,,
2:00 AM,,,,
...
9:00 AM,Meeting prep [Planning],Review budget [Finance],Catering setup [Catering] @ Kitchen,
10:00 AM,Meeting prep [Planning],Review budget [Finance] | Send invites [Communication],Catering setup [Catering] @ Kitchen,
...
```

### Cell Content Format:
Each cell contains task information in the format:
`Task Name [Type] (Status) @ Location`

- Multiple tasks in the same time slot are separated by ` | `
- Optional elements (type, status, location) are only included if present
- Tasks that span multiple time slots appear in all relevant rows

## Features

- **Timeline Grid Layout**: Matches the visual structure of the Daily View with time slots as rows and assignees as columns

- **Automatic Time Calculation**: Task overlaps with time slots are calculated based on:
  1. Start time + duration (if duration is specified)
  2. Soft deadline (if no duration)
  3. Hard deadline (if no duration or soft deadline)
  4. Default 1-hour duration (if none of the above)

- **Multi-task Handling**: Multiple tasks in the same time slot are separated by ` | ` for easy reading

- **Rich Task Information**: Each task shows name, type, status, and location when available

- **Group Support**: Assignee groups are properly labeled and exported as separate columns

- **Proper CSV Escaping**: Fields containing commas, quotes, newlines, or pipes are properly escaped and quoted

- **Internationalization**: Button text and tooltips support both English and Chinese translations

- **Date-specific Export**: Only exports tasks for the currently selected date

- **Hourly Granularity**: 24-hour timeline with 1-hour time slots (12:00 AM to 11:00 PM)

## Technical Implementation

The CSV export functionality is implemented directly in the DailyView component with:

- Client-side CSV generation (no server round-trip required)
- Proper CSV formatting with RFC 4180 compliance
- Automatic file download using browser APIs
- Responsive UI integration with existing toolbar

## Translation Keys

The feature uses the following translation keys:

- `calendar.dailyView.exportCSV`: Button text
- `calendar.dailyView.exportCSVTooltip`: Tooltip text

## Browser Compatibility

The feature works in all modern browsers that support:
- Blob API
- URL.createObjectURL()
- HTML5 download attribute

This includes Chrome, Firefox, Safari, and Edge (modern versions).
