// Test data for CSV export functionality
const mockTasks = [
  {
    _id: '1',
    name: 'Setup venue',
    taskType: 'Setup',
    status: 'In Progress',
    startTime: '2024-01-15T09:00:00Z',
    duration: '02:00:00',
    assignees: [
      { name: '<PERSON>', email: '<EMAIL>' },
      { name: '<PERSON>', email: '<EMAIL>' }
    ],
    location: 'Main Hall',
    details: 'Setup tables and chairs for the event',
    softDeadline: '2024-01-15T11:00:00Z',
    hardDeadline: '2024-01-15T12:00:00Z'
  },
  {
    _id: '2',
    name: 'Catering preparation',
    taskType: 'Catering',
    status: 'Not Started',
    startTime: '2024-01-15T10:30:00Z',
    duration: '01:30:00',
    assignees: [],
    location: 'Kitchen',
    details: 'Prepare food for 100 guests',
    softDeadline: '2024-01-15T12:00:00Z',
    hardDeadline: null
  }
];

// Function to generate CSV content (extracted from DailyView component)
const generateCSVContent = (tasks, currentDate) => {
  const csvData = [];
  
  // Add header row
  csvData.push([
    'Task Name',
    'Type',
    'Status',
    'Start Time',
    'End Time',
    'Duration',
    'Assignees',
    'Location',
    'Details',
    'Soft Deadline',
    'Hard Deadline'
  ]);

  // Process tasks for the current day
  tasks.forEach(task => {
    const startTime = task.startTime ? new Date(task.startTime) : null;
    let endTime = null;
    
    // Calculate end time
    if (task.duration && task.duration !== '00:00:00') {
      const [hours, minutes, seconds] = task.duration.split(':').map(Number);
      endTime = new Date(startTime);
      endTime.setHours(endTime.getHours() + hours);
      endTime.setMinutes(endTime.getMinutes() + minutes);
      endTime.setSeconds(endTime.getSeconds() + seconds);
    } else if (task.softDeadline) {
      endTime = new Date(task.softDeadline);
    } else if (task.hardDeadline) {
      endTime = new Date(task.hardDeadline);
    } else if (startTime) {
      // Default 1-hour duration
      endTime = new Date(startTime.getTime() + 60 * 60 * 1000);
    }

    // Format assignees
    const assigneeNames = task.assignees && task.assignees.length > 0 
      ? task.assignees.map(assignee => assignee.name || assignee.email || 'Unknown').join(', ')
      : 'Unassigned';

    // Format times
    const formatTime = (date) => date ? date.toLocaleTimeString('en-US', { hour12: false }) : '';
    const formatDateTime = (date) => date ? date.toLocaleString('en-US') : '';

    csvData.push([
      task.name || '',
      task.taskType || '',
      task.status || '',
      formatTime(startTime),
      formatTime(endTime),
      task.duration || '',
      assigneeNames,
      task.location || '',
      task.details || '',
      formatDateTime(task.softDeadline ? new Date(task.softDeadline) : null),
      formatDateTime(task.hardDeadline ? new Date(task.hardDeadline) : null)
    ]);
  });

  // Convert to CSV string
  const csvContent = csvData.map(row => 
    row.map(field => {
      // Escape quotes and wrap in quotes if contains comma, quote, or newline
      const stringField = String(field || '');
      if (stringField.includes(',') || stringField.includes('"') || stringField.includes('\n')) {
        return `"${stringField.replace(/"/g, '""')}"`;
      }
      return stringField;
    }).join(',')
  ).join('\n');

  return csvContent;
};

// Test the CSV generation
console.log('Testing CSV Export Functionality');
console.log('================================');

const testDate = new Date('2024-01-15');
const csvContent = generateCSVContent(mockTasks, testDate);

console.log('Generated CSV Content:');
console.log(csvContent);

console.log('\nExpected CSV structure:');
console.log('- Header row with 11 columns');
console.log('- Task rows with proper escaping for commas and quotes');
console.log('- Assignees joined with commas');
console.log('- Proper time formatting');

// Validate the CSV content
const lines = csvContent.split('\n');
console.log(`\nValidation Results:`);
console.log(`- Total lines: ${lines.length} (expected: 3 - header + 2 tasks)`);
console.log(`- Header columns: ${lines[0].split(',').length} (expected: 11)`);
console.log(`- First task assignees: ${lines[1].includes('John Doe, Jane Smith') ? 'PASS' : 'FAIL'}`);
console.log(`- Second task unassigned: ${lines[2].includes('Unassigned') ? 'PASS' : 'FAIL'}`);
