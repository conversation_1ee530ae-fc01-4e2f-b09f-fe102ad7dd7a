import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  TextField,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Grid,
  FormHelperText,
  InputAdornment,
  Chip,
  Paper,
  Divider,
  Autocomplete,
  FormControlLabel,
  Checkbox,
  Switch,
  Alert,
  Avatar,
  CircularProgress
} from '@mui/material';
import config from '../config';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { LocalizationProvider, DateTimePicker } from '@mui/x-date-pickers';
import AttachFileIcon from '@mui/icons-material/AttachFile';
import BudgetItemsSection from './BudgetItemsSection';
import SubtasksSection from './SubtasksSection';
import SuppliesSection from './SuppliesSection';

const TASK_TYPES = [
  'Venue',
  'Catering',
  'Photography',
  'Decoration',
  'Entertainment',
  'Logistics',
  'Other'
];

const TASK_STATUSES = [
  'Not Started',
  'In Progress',
  'Completed',
  'Delayed',
  'Cancelled'
];

const TaskForm = ({ task, onSubmit, allTasks, assignees, eventId }) => {
  const { t } = useTranslation();
  // Log the eventId to debug
  console.log("TaskForm received eventId:", eventId);

  // Add loading state
  const [loading, setLoading] = useState(!task);

  const initialFormState = {
    name: '',
    taskType: 'Other', // Default to 'Other' type
    startTime: null,
    duration: '00:00:00', // Duration in format hh:mm:ss
    location: '',
    details: '',
    cost: {
      amount: 0,
      currency: 'USD',
      isPaid: false
    },
    // Add budgetItems array for multiple budget items
    budgetItems: [],
    // Add supplies array for wedding supplies
    supplies: [],
    assignees: [],
    softDeadline: null,
    hardDeadline: null,
    dependencies: [],
    parentTask: null, // Keep for backward compatibility
    subtasks: [], // Array for storing subtask references (can contain full objects)
    subtaskIds: [], // Array for storing subtask IDs for submission
    status: 'Not Started',
    event: eventId || '' // Make sure eventId is set here
  };

  const [formData, setFormData] = useState({
    ...initialFormState,
    event: eventId || '' // Also set it here to be safe
  });
  const [formErrors, setFormErrors] = useState({});
  const [availableDependencies, setAvailableDependencies] = useState([]);

  // Initialize form with task data if editing
  useEffect(() => {
    // Set loading state based on whether we have a task
    setLoading(!task);

    if (task) {
      console.log('Task received in TaskForm:', JSON.stringify(task, null, 2));
      console.log('Available assignees:', JSON.stringify(assignees, null, 2));

      // If we have a task but it doesn't have an _id, we're still loading
      if (!task._id) {
        setLoading(true);
        return;
      }

      const taskCopy = { ...task };

      // Ensure cost object exists
      if (!taskCopy.cost) {
        taskCopy.cost = {
          amount: 0,
          currency: 'USD',
          isPaid: false
        };
      }

      // Ensure budgetItems is properly initialized
      if (!taskCopy.budgetItems || !Array.isArray(taskCopy.budgetItems)) {
        taskCopy.budgetItems = [];
      }

      // Ensure supplies is properly initialized
      if (!taskCopy.supplies || !Array.isArray(taskCopy.supplies)) {
        taskCopy.supplies = [];
      }

      // Convert date strings to Date objects
      if (taskCopy.startTime) taskCopy.startTime = new Date(taskCopy.startTime);
      if (taskCopy.softDeadline) taskCopy.softDeadline = new Date(taskCopy.softDeadline);
      if (taskCopy.hardDeadline) taskCopy.hardDeadline = new Date(taskCopy.hardDeadline);

      // Ensure dependencies array is properly initialized with string IDs only
      if (taskCopy.dependencies && Array.isArray(taskCopy.dependencies)) {
        console.log('Processing dependencies:', JSON.stringify(taskCopy.dependencies, null, 2));

        // Extract just the IDs from the dependencies as strings
        const dependencyIds = taskCopy.dependencies.map(dep => {
          // If dependency is already a string ID
          if (typeof dep === 'string') {
            return dep.toString();
          }
          // If dependency is a populated object with _id
          else if (dep && dep._id) {
            console.log('Found populated dependency object:', dep.name, dep._id);
            return dep._id.toString();
          }
          return null;
        }).filter(id => id !== null);

        taskCopy.dependencies = dependencyIds;
        console.log('Processed dependency IDs:', JSON.stringify(taskCopy.dependencies, null, 2));
      } else {
        taskCopy.dependencies = [];
      }

      // Ensure assignee is properly formatted for the form
      // No need to modify the assignee object as it comes correctly formatted from the API
      // Just log it for debugging
      if (taskCopy.assignee) {
        console.log('Assignee in task:', JSON.stringify(taskCopy.assignee, null, 2));
        console.log('Assignee type:', typeof taskCopy.assignee);
      }

      // For subtasks, we want to keep the full objects for the SubtasksSection component
      // but also maintain the IDs for form submission
      if (taskCopy.subtasks && Array.isArray(taskCopy.subtasks)) {
        console.log('[TaskForm] RECEIVED SUBTASKS FROM SERVER:', JSON.stringify(taskCopy.subtasks, (key, value) => {
          // Handle circular references in objects
          if (key === '_id' && typeof value === 'object') return value.toString();
          return value;
        }, 2));
        console.log('[TaskForm] Subtasks types:', taskCopy.subtasks.map(s => typeof s));
        console.log('[TaskForm] Subtasks structure:', taskCopy.subtasks.map(s => {
          if (typeof s === 'string') return { type: 'string', value: s };
          if (s && typeof s === 'object') {
            return {
              type: 'object',
              hasId: Boolean(s._id),
              hasName: Boolean(s.name),
              keys: Object.keys(s)
            };
          }
          return { type: typeof s, value: s };
        }));

        // Check if all subtasks are already full objects with required fields
        const allAreCompleteObjects = taskCopy.subtasks.every(subtask =>
          subtask && typeof subtask === 'object' && subtask._id && subtask.name && subtask.taskType && subtask.status
        );

        if (allAreCompleteObjects) {
          console.log('[TaskForm] All subtasks are already complete objects, using as is');

          // Extract IDs for form submission
          const subtaskIds = taskCopy.subtasks.map(subtask => subtask._id.toString());
          taskCopy.subtaskIds = subtaskIds;

          console.log(`[TaskForm] Task has ${subtaskIds.length} complete subtask objects`);
        } else {
          // Keep the original subtasks array with populated objects
          // This will be used by the SubtasksSection component
          const originalSubtasks = [];

          // Process each subtask to ensure it has all required fields
          taskCopy.subtasks.forEach((subtask, index) => {
            if (typeof subtask === 'string') {
              // If it's a string ID, try to find the corresponding task in availableDependencies
              const foundTask = availableDependencies.find(t => t._id.toString() === subtask);
              if (foundTask) {
                originalSubtasks.push(foundTask);
              } else {
                // If we can't find the task, create a minimal task object
                originalSubtasks.push({
                  _id: subtask,
                  name: `Task ${subtask.substring(0, 6)}...`,
                  taskType: 'Other',
                  status: 'Not Started'
                });
              }
            } else if (subtask && typeof subtask === 'object' && subtask._id) {
              // If it's an object with an ID, ensure it has all required fields
              const processedSubtask = {
                ...subtask,
                name: subtask.name || 'Unnamed Task',
                taskType: subtask.taskType || 'Other',
                status: subtask.status || 'Not Started'
              };
              originalSubtasks.push(processedSubtask);
            }
          });

          // Extract just the IDs from the subtasks as strings for form submission
          const subtaskIds = taskCopy.subtasks.map((subtask, index) => {
            // If subtask is already a string ID
            if (typeof subtask === 'string') {
              console.log(`[TaskForm] Subtask ${index} is already a string ID:`, subtask);
              return subtask.toString();
            }
            // If subtask is a populated object with _id
            else if (subtask && subtask._id) {
              console.log(`[TaskForm] Subtask ${index} is a populated object:`, {
                id: subtask._id.toString(),
                name: subtask.name || 'unnamed',
                type: subtask.taskType || 'unknown',
                status: subtask.status || 'unknown'
              });
              return subtask._id.toString();
            }
            console.warn(`[TaskForm] Subtask ${index} has invalid format:`, subtask);
            return null;
          }).filter(id => id !== null);

          // Store both the IDs and the original objects
          // Make sure we keep the full subtask objects for display in the form
          taskCopy.subtasks = originalSubtasks;
          taskCopy.subtaskIds = subtaskIds;

          console.log('[TaskForm] Processed subtasks IDs:', JSON.stringify(subtaskIds, null, 2));
          console.log(`[TaskForm] Task has ${subtaskIds.length} subtasks out of ${taskCopy.subtasks.length} total`);

          // If we have subtasks, log them for debugging
          if (subtaskIds.length > 0) {
            console.log('[TaskForm] Subtask IDs after processing:');
            subtaskIds.forEach((id, index) => console.log(`- Subtask ${index}: ${id}`));
          }

          // Log the actual subtask objects for debugging
          console.log('[TaskForm] Subtask objects after processing:');
          taskCopy.subtasks.forEach((subtask, index) => {
            if (typeof subtask === 'object' && subtask !== null) {
              console.log(`- Subtask ${index}: ${subtask.name || 'unnamed'} (${subtask._id})`);
            } else {
              console.log(`- Subtask ${index}: ${subtask}`);
            }
          });
        }
      } else {
        console.log('[TaskForm] No subtasks found or not an array, initializing empty array');
        taskCopy.subtasks = [];
        taskCopy.subtaskIds = [];
      }

      console.log('Task copy after processing:', JSON.stringify(taskCopy, null, 2));
      setFormData(taskCopy);
    } else {
      setFormData({
        name: '',
        details: '',
        taskType: 'Other', // Default to 'Other' type
        status: 'Not Started',
        cost: {
          amount: 0,
          currency: 'USD',
          isPaid: false
        },
        budgetItems: [], // Initialize empty budget items array
        supplies: [], // Initialize empty supplies array
        assignees: [],
        softDeadline: null,
        hardDeadline: null,
        dependencies: [],
        parentTask: null, // Initialize parent task as null
        subtasks: [], // Initialize subtasks as empty array
        subtaskIds: [], // Initialize subtask IDs as empty array
        event: eventId || ''
      });
    }
    // Log the form data after setting it
    console.log("Form data after initialization:", formData);

    // Turn off loading state after form data is set
    setLoading(false);
  }, [task, eventId, assignees]);

  // Filter out current task from dependencies and sort by name
  useEffect(() => {
    if (allTasks) {
      console.log('Setting up available dependencies from allTasks:', allTasks.length);

      const filteredTasks = allTasks
        .filter(t => t._id !== (task?._id || ''))
        .sort((a, b) => {
          // Add null checks to prevent errors
          const nameA = a.name || '';
          const nameB = b.name || '';
          return nameA.localeCompare(nameB);
        });

      console.log('Filtered tasks for dependencies/subtasks:', filteredTasks.length);
      setAvailableDependencies(filteredTasks);

      // Log the current subtasks for debugging
      if (formData.subtasks && Array.isArray(formData.subtasks) && formData.subtasks.length > 0) {
        console.log('Current subtasks in formData:', formData.subtasks);

        // Check if any of the subtasks match available dependencies
        const matchingTasks = filteredTasks.filter(t => {
          const taskIdStr = t._id.toString();

          return formData.subtasks.some(subtaskId => {
            // Convert subtaskId to string for comparison
            const subtaskIdStr = typeof subtaskId === 'string' ? subtaskId :
              (subtaskId && subtaskId._id ? subtaskId._id.toString() : '');

            const matches = subtaskIdStr === taskIdStr;
            if (matches) {
              console.log('Found matching task for subtask:', t.name, taskIdStr, '=', subtaskIdStr);
            }
            return matches;
          });
        });

        console.log('Matching tasks for subtasks:', matchingTasks.length);
        if (matchingTasks.length > 0) {
          console.log('Matching tasks:');
          matchingTasks.forEach(t => console.log(`- ${t.name} (${t._id})`));
        }
      }
    }
  }, [allTasks, task, formData.subtasks]);

  const validate = () => {
    const newErrors = {};

    if (!formData.name) {
      newErrors.name = t('taskForm.errors.nameRequired', 'Task name is required');
    }

    if (!formData.taskType) {
      newErrors.taskType = t('taskForm.errors.typeRequired', 'Task type is required');
    }

    if (!formData.event) {
      newErrors.event = t('taskForm.errors.eventRequired', 'Event is required');
      console.log("Event validation failed. Current event value:", formData.event);
      console.log("Provided eventId:", eventId);
    }

    setFormErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleChange = (e) => {
    const { name, value } = e.target;

    if (name.includes('.')) {
      // Handle nested properties (e.g., cost.amount)
      const [parent, child] = name.split('.');
      setFormData({
        ...formData,
        [parent]: {
          ...formData[parent],
          [child]: value
        }
      });
    } else if (name === 'assignee') {
      // Special handling for assignee selection
      console.log('Assignee selection changed to:', value);

      if (value === '') {
        // Handle unassigned case
        console.log('Setting assignee to null (unassigned)');
        setFormData({
          ...formData,
          assignee: null
        });
      } else {
        // Find the full user object for the selected assignee ID
        const selectedUser = assignees.find(user => user._id === value);
        if (selectedUser) {
          console.log('Selected user found:', selectedUser);
          setFormData({
            ...formData,
            assignee: selectedUser
          });
        } else {
          console.warn('Selected user not found for ID:', value);
          // If user not found, still update with the ID to prevent UI issues
          setFormData({
            ...formData,
            assignee: { _id: value, name: 'Unknown User' }
          });
        }
      }

      // Log the updated form data for debugging
      setTimeout(() => {
        console.log('Form data after assignee update:', formData);
      }, 0);
    } else {
      setFormData({
        ...formData,
        [name]: value
      });
    }
  };

  const handleDateChange = (name, value) => {
    setFormData({
      ...formData,
      [name]: value
    });
  };

  const handleDependencyChange = (e) => {
    setFormData({
      ...formData,
      dependencies: e.target.value
    });
  };

  // Helper function to check if a string is a valid MongoDB ObjectId
  const isValidObjectId = (id) => {
    return id && /^[0-9a-fA-F]{24}$/.test(id);
  };

  // State for tracking if we're currently loading users
  const [loadingUsers, setLoadingUsers] = useState(false);
  const [searchQuery, setSearchQuery] = useState('');
  const [filteredAssignees, setFilteredAssignees] = useState([]);

  // Function to search for users from the database
  const searchUsers = async (query) => {
    if (!query || query.length < 2) {
      // Don't search if query is too short
      return;
    }

    setLoadingUsers(true);
    try {
      // Make API call to search for users
      const response = await fetch(`${config.API_URL}/users/search?q=${encodeURIComponent(query)}`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      });

      if (!response.ok) {
        throw new Error(`Error searching users: ${response.statusText}`);
      }

      const data = await response.json();
      console.log('User search results:', data);

      // Merge search results with existing assignees to avoid duplicates
      const existingIds = new Set(assignees.map(user => user._id));
      const newUsers = data.filter(user => !existingIds.has(user._id));

      if (newUsers.length > 0) {
        // Add new users to the assignees list
        const updatedAssignees = [...assignees, ...newUsers];
        setFilteredAssignees(updatedAssignees);
      } else {
        setFilteredAssignees(assignees);
      }
    } catch (error) {
      console.error('Error searching for users:', error);
    } finally {
      setLoadingUsers(false);
    }
  };

  // Effect to filter assignees based on search query
  useEffect(() => {
    if (!assignees) {
      setFilteredAssignees([]);
      return;
    }

    // Initialize filtered assignees with the current assignees
    setFilteredAssignees(assignees);

    if (searchQuery && searchQuery.length >= 2) {
      // Debounce the search to avoid too many API calls
      const debounceTimeout = setTimeout(() => {
        searchUsers(searchQuery);
      }, 500);

      return () => clearTimeout(debounceTimeout);
    }
  }, [assignees, searchQuery]);

  const [submissionError, setSubmissionError] = useState('');

  const validateForm = () => {
    const errors = {};

    // Required fields validation
    if (!formData.name || formData.name.trim() === '') {
      errors.name = t('taskForm.errors.nameRequired', 'Task name is required');
    }

    if (!formData.taskType || formData.taskType.trim() === '') {
      errors.taskType = t('taskForm.errors.typeRequired', 'Task type is required');
    }

    // Validate assignees - allow both real MongoDB users and non-user assignees with names
    if (formData.assignees && formData.assignees.length > 0) {
      // Check if all assignees are either valid users or have a name (non-user assignees)
      const invalidAssignees = formData.assignees.filter(assignee => {
        // If it's a non-user assignee, it must have a name
        if (assignee.isNonUserAssignee) {
          return !assignee.name || assignee.name.trim() === '';
        }

        // If it's a regular user, it must have a valid ID that exists in the assignees list
        const assigneeId = typeof assignee === 'string' ? assignee : assignee._id;
        if (!assigneeId || !isValidObjectId(assigneeId)) return true;

        // Check if this ID exists in the assignees list
        return !assignees.some(user => user._id === assigneeId);
      });

      if (invalidAssignees.length > 0) {
        errors.assignees = t('taskForm.errors.invalidAssignees', 'One or more selected assignees are invalid');
      }
    }

    // Validate subtasks selection
    if (formData.subtasks && formData.subtasks.length > 0) {
      console.log('[TaskForm] Validating subtasks:', formData.subtasks);

      // Extract subtask IDs for validation
      const subtaskIds = formData.subtasks.map(subtask => {
        if (typeof subtask === 'string') {
          return subtask;
        } else if (subtask && subtask._id) {
          return subtask._id.toString();
        }
        return null;
      }).filter(id => id !== null);

      console.log('[TaskForm] Extracted subtask IDs for validation:', subtaskIds);

      // Prevent self-reference
      if (task && task._id && subtaskIds.includes(task._id.toString())) {
        errors.subtasks = t('taskForm.errors.selfReference', 'A task cannot be its own subtask');
        console.log('[TaskForm] Self-reference detected in subtasks');
      }

      // For existing subtasks that are full objects, we don't need to validate them against availableDependencies
      // Only validate string IDs that might not be valid anymore
      const stringSubtaskIds = formData.subtasks
        .filter(subtask => typeof subtask === 'string')
        .map(id => id.toString());

      if (stringSubtaskIds.length > 0) {
        console.log('[TaskForm] Validating string subtask IDs:', stringSubtaskIds);

        // Check if all string subtask IDs exist in available tasks
        const invalidSubtasks = stringSubtaskIds.filter(subtaskId =>
          !availableDependencies.some(t => t._id.toString() === subtaskId)
        );

        if (invalidSubtasks.length > 0) {
          console.log('[TaskForm] Invalid subtasks found:', invalidSubtasks);
          errors.subtasks = t('taskForm.errors.invalidSubtasks', 'One or more selected subtasks are invalid');
        }
      }
    }

    // Keep parent task validation for backward compatibility
    if (formData.parentTask) {
      // Prevent self-reference
      if (task && formData.parentTask === task._id) {
        errors.parentTask = t('taskForm.errors.selfReference', 'A task cannot be its own parent');
      }

      // Check if parent task exists in available tasks
      if (!availableDependencies.some(t => t._id === formData.parentTask)) {
        errors.parentTask = t('taskForm.errors.invalidParent', 'Selected parent task is invalid');
      }
    }

    setFormErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleSubmit = (e) => {
    e.preventDefault();
    setSubmissionError('');

    // Validate the form
    if (!validateForm()) {
      setSubmissionError(t('taskForm.errors.fixErrors', 'Please fix the errors in the form before submitting'));
      return;
    }

    // Log the form data before submission for debugging
    console.log('Form data before submission:', JSON.stringify(formData, null, 2));

    // Create a clean copy of the form data for submission
    const taskToSubmit = { ...formData };

    // Handle assignees field - keep both user IDs and non-user assignees
    console.log('Assignees value:', formData.assignees);

    if (formData.assignees && formData.assignees.length > 0) {
      console.log('Processing assignees:', JSON.stringify(formData.assignees, null, 2));

      // Process each assignee - could be a user ID, user object, or non-user assignee
      const processedAssignees = formData.assignees.map(assignee => {
        // If it's a non-user assignee with a name
        if (assignee.isNonUserAssignee || (!assignee._id && assignee.name)) {
          // Ensure it has the isNonUserAssignee flag
          return {
            name: assignee.name,
            isNonUserAssignee: true,
            ...(assignee.email && { email: assignee.email }),
            ...(assignee.phone && { phone: assignee.phone }),
            ...(assignee.notes && { notes: assignee.notes })
          };
        }

        // If assignee is already a string ID
        if (typeof assignee === 'string') {
          // Verify it's a valid MongoDB ID that exists in the assignees list
          if (isValidObjectId(assignee) && assignees.some(user => user._id === assignee)) {
            return assignee;
          }
          // If it's just a string but not a valid ID, treat it as a non-user assignee name
          return {
            name: assignee,
            isNonUserAssignee: true
          };
        }

        // If assignee is an object with _id property (user object)
        if (assignee && assignee._id) {
          // Verify it's a valid MongoDB ID that exists in the assignees list
          if (isValidObjectId(assignee._id) && assignees.some(user => user._id === assignee._id)) {
            return assignee._id;
          }
        }

        return null;
      }).filter(assignee => assignee !== null);

      console.log('Processed assignees:', processedAssignees);
      taskToSubmit.assignees = processedAssignees;
    } else {
      console.log('No assignees selected, setting to empty array');
      taskToSubmit.assignees = [];
    }

    // Ensure assignees are properly set in the final task data
    console.log('Assignees after processing:', taskToSubmit.assignees);

    // Process budget items - ensure they are properly formatted
    if (formData.budgetItems && Array.isArray(formData.budgetItems)) {
      console.log('Processing budget items:', JSON.stringify(formData.budgetItems, null, 2));

      // Format budget items - convert string values to numbers where needed
      taskToSubmit.budgetItems = formData.budgetItems.map(item => {
        // Create a clean copy without any MongoDB-specific fields
        const cleanItem = { ...item };

        // Remove MongoDB-specific fields if they exist
        delete cleanItem._id;
        delete cleanItem.__v;

        return {
          ...cleanItem,
          // Ensure numeric fields are numbers, not strings
          estimatedAmount: parseFloat(cleanItem.estimatedAmount) || 0,
          actualAmount: parseFloat(cleanItem.actualAmount) || 0,
          // Ensure boolean fields are booleans
          isPaid: Boolean(cleanItem.isPaid),
          // Add timestamps if not present
          createdAt: cleanItem.createdAt || new Date(),
          updatedAt: new Date(),
          // Ensure required fields have values
          description: cleanItem.description || 'Unnamed Budget Item',
          category: cleanItem.category || 'Other',
          currency: cleanItem.currency || 'USD'
        };
      });

      console.log('Formatted budget items:', JSON.stringify(taskToSubmit.budgetItems, null, 2));
    } else {
      // Ensure budgetItems is always an array
      taskToSubmit.budgetItems = [];
    }

    // Process supplies - ensure they are properly formatted
    if (formData.supplies && Array.isArray(formData.supplies)) {
      console.log('Processing supplies:', JSON.stringify(formData.supplies, null, 2));

      // Make sure we only have valid string IDs in the supplies array
      const supplyIds = formData.supplies
        .map((supplyId, index) => {
          // If supply is already a string ID
          if (typeof supplyId === 'string') {
            console.log(`[TaskForm] Supply ${index} is already a string ID:`, supplyId);
            return supplyId.toString();
          }
          // If supply is an object with _id
          else if (supplyId && supplyId._id) {
            console.log(`[TaskForm] Supply ${index} is an object with _id:`, {
              id: supplyId._id.toString()
            });
            return supplyId._id.toString();
          }
          console.warn(`[TaskForm] Supply ${index} has invalid format:`, supplyId);
          return null;
        })
        .filter(id => id !== null && isValidObjectId(id));

      console.log('Formatted supplies:', supplyIds);
      taskToSubmit.supplies = supplyIds;
    } else {
      // Ensure supplies is always an array
      taskToSubmit.supplies = [];
    }

    // Handle task update vs. create
    if (task && task._id) {
      console.log('Updating existing task with ID:', task._id);

      // For task updates, we need to preserve the _id but clean up other fields
      taskToSubmit._id = task._id;

      // Remove metadata fields that should be handled by the server
      delete taskToSubmit.createdAt;
      delete taskToSubmit.updatedAt;
      delete taskToSubmit.__v;

      // If event is an object, extract just the ID
      if (taskToSubmit.event && typeof taskToSubmit.event === 'object') {
        taskToSubmit.event = taskToSubmit.event._id;
      }
    } else {
      console.log('Creating new task');
    }

    // Process subtasks - use the subtaskIds array if available, otherwise extract from subtasks
    console.log('[TaskForm] PREPARING SUBTASKS FOR SUBMISSION');
    console.log('[TaskForm] Form data subtasks:', formData.subtasks ? {
      length: formData.subtasks.length,
      types: formData.subtasks.map(s => typeof s),
      sample: formData.subtasks.slice(0, 2).map(s => {
        if (typeof s === 'string') return { type: 'string', value: s };
        if (s && typeof s === 'object') {
          return {
            type: 'object',
            hasId: Boolean(s._id),
            hasName: Boolean(s.name),
            id: s._id ? s._id.toString() : 'none',
            name: s.name || 'unnamed'
          };
        }
        return { type: typeof s, value: s };
      })
    } : 'not available');
    console.log('[TaskForm] Form data subtaskIds:', formData.subtaskIds ? {
      length: formData.subtaskIds.length,
      sample: formData.subtaskIds.slice(0, 5)
    } : 'not available');

    // Always extract IDs from the subtasks array to ensure we have the most up-to-date list
    if (formData.subtasks && Array.isArray(formData.subtasks)) {
      console.log('[TaskForm] Extracting subtask IDs from subtasks array for submission:',
        JSON.stringify(formData.subtasks, (key, value) => {
          // Handle circular references in objects
          if (key === '_id' && typeof value === 'object') return value.toString();
          return value;
        }, 2));

      // Make sure we only have valid string IDs in the subtasks array
      const subtaskIds = formData.subtasks
        .map((subtask, index) => {
          // If subtask is already a string ID
          if (typeof subtask === 'string') {
            console.log(`[TaskForm] Subtask ${index} is already a string ID:`, subtask);
            return subtask.toString();
          }
          // If subtask is an object with _id
          else if (subtask && subtask._id) {
            console.log(`[TaskForm] Subtask ${index} is an object with _id:`, {
              id: subtask._id.toString(),
              name: subtask.name || 'unnamed'
            });
            return subtask._id.toString();
          }
          console.warn(`[TaskForm] Subtask ${index} has invalid format:`, subtask);
          return null;
        })
        .filter(id => id !== null && isValidObjectId(id));

      console.log('[TaskForm] Extracted subtask IDs for submission:', subtaskIds);
      taskToSubmit.subtasks = subtaskIds;

      // Log the number of subtasks being submitted
      console.log(`[TaskForm] Submitting ${subtaskIds.length} subtasks out of ${formData.subtasks.length} total`);
      if (subtaskIds.length > 0) {
        console.log('[TaskForm] Subtask IDs being submitted:');
        subtaskIds.forEach((id, index) => console.log(`- Subtask ${index}: ${id}`));
      }
    } else if (formData.subtaskIds && Array.isArray(formData.subtaskIds)) {
      // Use the pre-processed subtaskIds array as a fallback
      console.log('[TaskForm] Using pre-processed subtaskIds for submission:', JSON.stringify(formData.subtaskIds, null, 2));

      // Filter to ensure we only have valid string IDs
      const validSubtaskIds = formData.subtaskIds
        .filter(id => id && typeof id === 'string' && isValidObjectId(id))
        .map(id => id.toString()); // Ensure all IDs are strings

      console.log('[TaskForm] Filtered subtask IDs for submission:', validSubtaskIds);
      taskToSubmit.subtasks = validSubtaskIds;

      // Log the number of subtasks being submitted
      console.log(`[TaskForm] Submitting ${validSubtaskIds.length} subtasks out of ${formData.subtaskIds.length} total`);
      if (validSubtaskIds.length > 0) {
        console.log('[TaskForm] Subtask IDs being submitted:');
        validSubtaskIds.forEach((id, index) => console.log(`- Subtask ${index}: ${id}`));
      }
    } else {
      console.log('[TaskForm] No subtasks to submit, setting empty array');
      taskToSubmit.subtasks = [];
    }

    // Remove the subtaskIds property as it's not needed in the backend
    delete taskToSubmit.subtaskIds;

    // Log the final task object being submitted
    console.log('[TaskForm] FINAL TASK OBJECT FOR SUBMISSION:', {
      ...taskToSubmit,
      subtasks: taskToSubmit.subtasks ? {
        length: taskToSubmit.subtasks.length,
        ids: taskToSubmit.subtasks
      } : []
    });

    console.log('Final subtasks array:', taskToSubmit.subtasks);

    // Log the final task data being submitted
    console.log('Task data being submitted:', JSON.stringify(taskToSubmit, null, 2));
    console.log('Final assignees value:', taskToSubmit.assignees);
    console.log('Final budget items:', JSON.stringify(taskToSubmit.budgetItems, null, 2));
    console.log('Final subtasks:', taskToSubmit.subtasks);
    console.log('Final supplies:', taskToSubmit.supplies);

    try {
      // Submit the task data
      onSubmit(taskToSubmit);
    } catch (error) {
      // If an error occurs, show it in the form
      console.error('Error submitting task:', error);
      setSubmissionError(error.message || t('taskForm.errors.savingError', 'An error occurred while saving the task'));
    }
  };

  // Show loading indicator if we're waiting for task data
  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '300px' }}>
        <CircularProgress />
        <Typography variant="body1" sx={{ ml: 2 }}>
          {t('taskForm.loadingTask', 'Loading task data...')}
        </Typography>
      </Box>
    );
  }

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Paper sx={{ p: 3 }}>
        <Typography variant="h5" gutterBottom>
          {task ? t('taskForm.editTask', 'Edit Task') : t('taskForm.createNewTask', 'Create New Task')}
        </Typography>

        <Divider sx={{ mb: 3 }} />

        <form onSubmit={handleSubmit}>
          <Grid container spacing={2}>
            {/* Task Name */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('taskForm.name', 'Task Name')}
                name="name"
                value={formData.name}
                onChange={handleChange}
                error={!!formErrors.name}
                helperText={formErrors.name}
                required
              />
            </Grid>

            {/* Task Type */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!formErrors.taskType} required>
                <InputLabel>{t('taskForm.type', 'Task Type')}</InputLabel>
                <Select
                  name="taskType"
                  value={formData.taskType}
                  onChange={handleChange}
                  label={t('taskForm.type', 'Task Type')}
                >
                  <MenuItem value="">{t('taskForm.selectType', 'Select a type')}</MenuItem>
                  {TASK_TYPES.map(type => (
                    <MenuItem key={type} value={type}>{type}</MenuItem>
                  ))}
                </Select>
                {formErrors.taskType && <FormHelperText>{formErrors.taskType}</FormHelperText>}
              </FormControl>
            </Grid>

            {/* Start Time */}
            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label={t('taskForm.startTime', 'Start Time')}
                value={formData.startTime}
                onChange={(value) => handleDateChange('startTime', value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: 'outlined'
                  }
                }}
              />
            </Grid>

            {/* Duration */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('taskForm.duration', 'Duration (hh:mm:ss)')}
                name="duration"
                type="text"
                value={formData.duration || '00:00:00'}
                onChange={(e) => {
                  // Accept all input and handle formatting on blur
                  setFormData({
                    ...formData,
                    duration: e.target.value
                  });
                }}
                onBlur={(e) => {
                  // Format the duration on blur to ensure it's in the correct format
                  let value = e.target.value.trim();

                  try {
                    // Handle empty input
                    if (value === '') {
                      value = '00:00:00';
                    }
                    // Handle hh:mm format (add seconds)
                    else if (/^\d{1,2}:\d{1,2}$/.test(value)) {
                      value = `${value}:00`;
                    }
                    // Handle single number (assume hours)
                    else if (/^\d{1,2}$/.test(value)) {
                      value = `${value}:00:00`;
                    }
                    // Handle other formats - try to extract numbers
                    else if (!/^\d{1,2}:\d{1,2}:\d{1,2}$/.test(value)) {
                      // Extract all numbers from the input
                      const numbers = value.match(/\d+/g);
                      if (numbers && numbers.length >= 3) {
                        value = `${numbers[0]}:${numbers[1]}:${numbers[2]}`;
                      } else if (numbers && numbers.length === 2) {
                        value = `${numbers[0]}:${numbers[1]}:00`;
                      } else if (numbers && numbers.length === 1) {
                        value = `${numbers[0]}:00:00`;
                      } else {
                        value = '00:00:00';
                      }
                    }

                    // Ensure each component has two digits and is valid
                    const parts = value.split(':');
                    if (parts.length === 3) {
                      // Validate and cap hours, minutes, seconds to reasonable values
                      let hours = Math.min(99, parseInt(parts[0], 10) || 0);
                      let minutes = Math.min(59, parseInt(parts[1], 10) || 0);
                      let seconds = Math.min(59, parseInt(parts[2], 10) || 0);

                      // Format with leading zeros
                      value = `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
                    }
                  } catch (error) {
                    // If any error occurs, default to 00:00:00
                    console.error('Error formatting duration:', error);
                    value = '00:00:00';
                  }

                  setFormData({
                    ...formData,
                    duration: value
                  });
                }}
                InputProps={{
                  inputProps: {
                    placeholder: '00:00:00'
                  }
                }}
                helperText="Format: hours:minutes:seconds (e.g., 01:30:00 for 1.5 hours)"
              />
            </Grid>

            {/* Computed End Time (read-only) */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label="End Time (computed)"
                InputProps={{
                  readOnly: true,
                }}
                value={(() => {
                  // Calculate end time based on start time and duration
                  if (!formData.startTime || !formData.duration) return 'Set start time and duration first';

                  try {
                    const startTime = new Date(formData.startTime);
                    const [hours, minutes, seconds] = formData.duration.split(':').map(Number);

                    const endTime = new Date(startTime);
                    endTime.setHours(endTime.getHours() + hours);
                    endTime.setMinutes(endTime.getMinutes() + minutes);
                    endTime.setSeconds(endTime.getSeconds() + seconds);

                    return endTime.toLocaleString();
                  } catch (error) {
                    return 'Invalid start time or duration';
                  }
                })()}
                helperText="Automatically calculated based on start time and duration"
              />
            </Grid>

            {/* Location */}
            <Grid item xs={12} sm={6}>
              <TextField
                fullWidth
                label={t('taskForm.location', 'Location')}
                name="location"
                value={formData.location}
                onChange={handleChange}
                placeholder={t('taskForm.locationPlaceholder', 'Where will this task take place?')}
              />
            </Grid>

            {/* Soft Deadline - Changed to DateTimePicker */}
            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label={t('taskForm.softDeadline', 'Soft Deadline')}
                value={formData.softDeadline}
                onChange={(value) => handleDateChange('softDeadline', value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: 'outlined'
                  }
                }}
              />
              <FormHelperText>Target completion date</FormHelperText>
            </Grid>

            {/* Hard Deadline - Changed to DateTimePicker */}
            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label={t('taskForm.hardDeadline', 'Hard Deadline')}
                value={formData.hardDeadline}
                onChange={(value) => handleDateChange('hardDeadline', value)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    variant: 'outlined'
                  }
                }}
              />
              <FormHelperText>Final deadline (cannot be moved)</FormHelperText>
            </Grid>

            {/* Status */}
            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>{t('taskForm.status', 'Status')}</InputLabel>
                <Select
                  name="status"
                  value={formData.status}
                  onChange={handleChange}
                  label={t('taskForm.status', 'Status')}
                >
                  {TASK_STATUSES.map(status => (
                    <MenuItem key={status} value={status}>{status}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Hidden Event Field - Add this to ensure event ID is set */}
            <input
              type="hidden"
              name="event"
              value={eventId || ''}
            />

            {/* Cost Amount */}
            <Grid item xs={12} sm={4}>
              <TextField
                fullWidth
                label={t('taskForm.cost', 'Cost')}
                name="cost.amount"
                type="number"
                value={formData.cost.amount}
                onChange={handleChange}
                InputProps={{
                  startAdornment: <InputAdornment position="start">$</InputAdornment>,
                }}
              />
            </Grid>

            {/* Currency */}
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>Currency</InputLabel>
                <Select
                  name="cost.currency"
                  value={formData.cost.currency}
                  onChange={handleChange}
                  label="Currency"
                >
                  {config.CURRENCIES.map(currency => (
                    <MenuItem key={currency} value={currency}>{currency}</MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Is Paid */}
            <Grid item xs={12} sm={4}>
              <FormControl fullWidth>
                <InputLabel>{t('taskForm.paymentStatus', 'Payment Status')}</InputLabel>
                <Select
                  name="cost.isPaid"
                  value={formData.cost.isPaid}
                  onChange={handleChange}
                  label={t('taskForm.paymentStatus', 'Payment Status')}
                >
                  <MenuItem value={false}>{t('taskForm.notPaid', 'Not Paid')}</MenuItem>
                  <MenuItem value={true}>{t('taskForm.paid', 'Paid')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Assignees */}
            <Grid item xs={12} sm={6}>
              <Autocomplete
                fullWidth
                multiple
                options={filteredAssignees || []}
                getOptionLabel={(option) => option.name || ''}
                value={formData.assignees || []}
                onChange={(_, newValue) => {
                  setFormData({
                    ...formData,
                    assignees: newValue
                  });
                }}
                onInputChange={(_, newInputValue) => {
                  setSearchQuery(newInputValue);
                }}
                loading={loadingUsers}
                freeSolo
                selectOnFocus
                clearOnBlur
                handleHomeEndKeys
                filterOptions={(options, params) => {
                  const filtered = options.filter(option => {
                    const optionLabel = option.name || '';
                    return optionLabel.toLowerCase().includes(params.inputValue.toLowerCase());
                  });

                  // Add option to create a new non-user assignee if input is not empty
                  if (params.inputValue !== '' && !filtered.some(option => option.name === params.inputValue)) {
                    filtered.push({
                      name: params.inputValue,
                      isNonUserAssignee: true,
                      inputValue: params.inputValue,
                      isNewOption: true
                    });
                  }

                  return filtered;
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label="Assignees"
                    error={!!formErrors.assignees}
                    helperText={formErrors.assignees || "Type a name and press Enter to add someone without an account"}
                    placeholder="Select or type a name"
                    InputProps={{
                      ...params.InputProps,
                      endAdornment: (
                        <>
                          {loadingUsers ? <CircularProgress color="inherit" size={20} /> : null}
                          {params.InputProps.endAdornment}
                        </>
                      ),
                    }}
                  />
                )}
                renderOption={(props, option) => {
                  // Check if this is a new option (non-user assignee)
                  if (option.isNewOption) {
                    return (
                      <li {...props}>
                        <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                          <Avatar sx={{ width: 24, height: 24, mr: 1, bgcolor: 'secondary.main' }}>
                            {option.name ? option.name.charAt(0).toUpperCase() : '?'}
                          </Avatar>
                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="body1">{option.name}</Typography>
                            <Typography variant="caption" color="text.secondary">
                              Add as non-user assignee
                            </Typography>
                          </Box>
                          <Chip
                            label="External"
                            size="small"
                            color="secondary"
                            variant="outlined"
                            sx={{ ml: 1 }}
                          />
                        </Box>
                      </li>
                    );
                  }

                  // Check if user has joined the event
                  const hasJoined = assignees.some(assignee =>
                    assignee._id === option._id &&
                    assignee.hasJoined
                  );

                  return (
                    <li {...props}>
                      <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                        <Avatar sx={{ width: 24, height: 24, mr: 1 }}>
                          {option.name ? option.name.charAt(0).toUpperCase() : '?'}
                        </Avatar>
                        <Box sx={{ flexGrow: 1 }}>
                          <Typography variant="body1">{option.name}</Typography>
                          {option.email && (
                            <Typography variant="caption" color="text.secondary">
                              {option.email}
                            </Typography>
                          )}
                        </Box>
                        {!hasJoined && (
                          <Chip
                            label="Not Joined"
                            size="small"
                            color="warning"
                            variant="outlined"
                            sx={{ ml: 1 }}
                          />
                        )}
                      </Box>
                    </li>
                  );
                }}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => {
                    // Determine if this is a non-user assignee
                    const isNonUser = option.isNonUserAssignee || (!option._id && option.name);

                    return (
                      <Chip
                        label={option.name || ''}
                        {...getTagProps({ index })}
                        key={option._id || `non-user-${index}`}
                        color={isNonUser ? "secondary" : "default"}
                      />
                    );
                  })
                }
              />
            </Grid>

            {/* Subtasks Section */}
            <Grid item xs={12}>
              <Box>
                <SubtasksSection
                  subtasks={formData.subtasks || []}
                  availableTasks={availableDependencies}
                  onChange={(newSubtasks) => {
                    console.log('Subtasks updated:', newSubtasks);
                    // Update both the subtasks array and the subtaskIds array
                    setFormData({
                      ...formData,
                      subtasks: newSubtasks,
                      subtaskIds: newSubtasks.map(subtask => {
                        return typeof subtask === 'string' ? subtask :
                          (subtask && subtask._id ? subtask._id.toString() : '');
                      }).filter(id => id)
                    });

                    // Clear any subtasks validation errors when subtasks are updated
                    if (formErrors.subtasks) {
                      setFormErrors({
                        ...formErrors,
                        subtasks: undefined
                      });
                    }
                  }}
                  currentTaskId={task?._id || ''}
                />
                {formErrors.subtasks && (
                  <FormHelperText error sx={{ ml: 2, mt: 1 }}>
                    {formErrors.subtasks}
                  </FormHelperText>
                )}
              </Box>
            </Grid>

            {/* Hidden Parent Task Field - Keep for backward compatibility */}
            <input
              type="hidden"
              name="parentTask"
              value={formData.parentTask || ''}
            />

            {/* Dependencies */}
            <Grid item xs={12}>
              <Autocomplete
                multiple
                options={availableDependencies}
                getOptionLabel={(option) => option.name || ''}
                value={availableDependencies.filter(task => {
                  // Check if this task's ID is in the dependencies array
                  if (!formData.dependencies || !Array.isArray(formData.dependencies) || formData.dependencies.length === 0) {
                    return false;
                  }

                  // Log for debugging
                  console.log('Checking if task is in dependencies:', task.name, task._id);
                  console.log('Current dependencies:', formData.dependencies);

                  // Check if this task's ID matches any dependency ID
                  const isSelected = formData.dependencies.some(depId => {
                    const taskIdStr = task._id.toString();
                    const depIdStr = typeof depId === 'string' ? depId :
                      (depId && depId._id ? depId._id.toString() : '');

                    const matches = taskIdStr === depIdStr;
                    if (matches) {
                      console.log('Found matching dependency:', task.name, taskIdStr, '=', depIdStr);
                    }
                    return matches;
                  });

                  return isSelected;
                })}
                onChange={(_, newValue) => {
                  // Extract just the IDs from the selected tasks as strings
                  const dependencyIds = newValue.map(t => t._id.toString());
                  console.log('Selected dependency IDs:', dependencyIds);

                  setFormData({
                    ...formData,
                    dependencies: dependencyIds
                  });
                }}
                renderInput={(params) => (
                  <TextField
                    {...params}
                    label={t('taskForm.dependencies', 'Dependencies')}
                    placeholder={t('taskForm.dependenciesPlaceholder', 'Select tasks that must be completed before this one')}
                  />
                )}
                renderTags={(value, getTagProps) =>
                  value.map((option, index) => (
                    <Chip
                      label={option.name || ''}
                      {...getTagProps({ index })}
                      key={option._id}
                    />
                  ))
                }
              />
            </Grid>

            {/* Details */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('taskForm.details', 'Details')}
                name="details"
                value={formData.details}
                onChange={handleChange}
                multiline
                rows={4}
                placeholder={t('taskForm.detailsPlaceholder', 'Enter any additional details about this task')}
              />
            </Grid>

            {/* Budget Items Section */}
            <Grid item xs={12}>
              <BudgetItemsSection
                budgetItems={formData.budgetItems || []}
                onChange={(updatedBudgetItems) => {
                  setFormData({
                    ...formData,
                    budgetItems: updatedBudgetItems
                  });
                }}
              />
            </Grid>

            {/* Supplies Section */}
            <Grid item xs={12}>
              <SuppliesSection
                taskId={task?._id}
                eventId={eventId}
                value={formData.supplies || []}
                onChange={(updatedSupplies) => {
                  setFormData({
                    ...formData,
                    supplies: updatedSupplies
                  });
                }}
              />
            </Grid>

            {/* File Attachments */}
            <Grid item xs={12}>
              <Button
                variant="outlined"
                startIcon={<AttachFileIcon />}
                disabled
              >
                Add Attachments
              </Button>
              <FormHelperText>File attachments will be implemented later</FormHelperText>
            </Grid>

            {/* Error Message */}
            {submissionError && (
              <Grid item xs={12}>
                <Alert severity="error" sx={{ mt: 2, mb: 2 }}>
                  {submissionError}
                </Alert>
              </Grid>
            )}

            {/* Submit Button */}
            <Grid item xs={12}>
              <Box sx={{ display: 'flex', justifyContent: 'flex-end', mt: 2 }}>
                <Button
                  type="submit"
                  variant="contained"
                  color="primary"
                  size="large"
                >
                  {task ? t('taskForm.updateTask', 'Update Task') : t('taskForm.createTask', 'Create Task')}
                </Button>
              </Box>
            </Grid>
          </Grid>
        </form>
      </Paper>
    </LocalizationProvider>
  );
};

export default TaskForm;