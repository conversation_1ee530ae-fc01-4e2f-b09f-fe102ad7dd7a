{"app": {"title": "Event Planner", "loading": "Loading..."}, "auth": {"login": "Sign In", "register": "Sign Up", "logout": "Logout", "profile": "Profile"}, "navigation": {"events": "Events", "tasks": "Tasks", "calendar": "Calendar", "budget": "Budget", "guests": "Guests", "venues": "Venues", "stakeholders": "Stakeholders", "supplies": "Supplies", "resources": "Resources", "pricing": "Pricing", "features": "Features", "home": "Home", "about": "About Us"}, "common": {"save": "Save", "cancel": "Cancel", "delete": "Delete", "edit": "Edit", "add": "Add", "search": "Search", "filter": "Filter", "noResults": "No results found", "loading": "Loading...", "success": "Success", "error": "Error", "warning": "Warning", "info": "Information", "free": "Free", "notSet": "Not set", "invalidDate": "Invalid date", "create": "Create", "update": "Update", "actions": "Actions", "all": "All", "requiredField": "Required field", "close": "Close", "more": "more", "cannotUndo": "This action cannot be undone.", "people": "people", "privacyPolicy": "Privacy Policy", "termsOfService": "Terms of Service"}, "events": {"title": "Events", "create": "Create Event", "select": "Select Event", "details": "Details", "noEvents": "No events found", "loadingEvents": "Loading events...", "noEventsAvailable": "No events available", "unnamedEvent": "Unnamed Event", "noDateSet": "No date set", "invalidDate": "Invalid date", "createFirstEvent": "Create your first event to get started with planning.", "generic": "Event", "manageTasks": "Manage Tasks", "editEvent": "Edit Event", "viewCalendar": "View Calendar", "manageMembers": "Manage Members", "deleteEvent": "Delete Event", "manageEventMembers": "Manage Event Members", "description": "Description", "venue": "Venue", "noVenueSelected": "No venue selected.", "budget": "Budget", "dateAndTime": "Date & Time", "at": "at", "daysRemaining": "{{days}} days remaining", "eventPassed": "Event has passed", "noDescriptionProvided": "No description provided.", "eventNotFound": "Event not found", "backToEvents": "Back to Events", "deleteConfirmation": "Are you sure you want to delete the event \"{{title}}\"? This action cannot be undone and will also delete all tasks associated with this event.", "taskProgress": "Task Progress", "noTasks": "No tasks created yet", "overBudget": "Over budget by {{amount}}", "underBudget": "Under budget by {{amount}}", "overBudgetTooltip": "Over budget! {{percent}}% of budget used", "budgetTooltip": "{{percent}}% of budget used", "overBudgetWarning": "Over budget by {{amount}}", "errors": {"loadFailed": "Failed to load events. Please try again later.", "createFailed": "Failed to create event. Please try again.", "updateFailed": "Failed to update event. Please try again.", "deleteFailed": "Failed to delete event. Please try again.", "loadDetailsFailed": "Failed to load event details"}}, "eventForm": {"title": "Title", "date": "Date", "eventType": "Event Type", "selectType": "Select a type", "location": "Location", "description": "Description", "descriptionPlaceholder": "Provide additional details about the event", "venueName": "Venue Name", "venueNamePlaceholder": "Where will the event be held?", "venueAddress": "Venue Address", "venueAddressPlaceholder": "Full address of the venue", "budget": "Budget", "template": "Template", "noTemplate": "None (Create from scratch)", "save": "Save", "cancel": "Cancel", "createEvent": "Create Event", "updateEvent": "Update Event"}, "tasks": {"title": "Tasks", "create": "Create Task", "edit": "Edit Task", "titleForEvent": "Tasks for {{eventTitle}}", "addDefault": "Add Default Tasks", "createNew": "Create New Task", "views": {"list": "List View", "dependencyGraph": "Dependency Graph"}, "filters": {"title": "Filters", "status": "Status", "taskType": "Task Type", "assignee": "Assignee", "dueDate": "Due Date", "sortBy": "Sort By", "search": "Search tasks...", "clearAll": "Clear All", "apply": "Apply Filters", "activeFilters": "Active Filters", "dueDates": {"all": "All Dates", "today": "Today", "week": "This Week", "month": "This Month", "overdue": "Overdue"}, "sortOptions": {"deadline": "Deadline", "name": "Name", "status": "Status", "type": "Type"}}, "exportImport": {"exportTasks": "Export Tasks", "importTasks": "Import Tasks", "exportDialogTitle": "Export Tasks", "exportDescription": "Below is the JSON representation of all tasks for this event. You can copy this data and use it to import tasks into another event.", "copyToClipboard": "Copy to clipboard", "close": "Close", "importDialogTitle": "Import Tasks", "jsonInput": "JSON Input", "fileUpload": "File Upload", "pasteJson": "Paste the JSON array of tasks below to import them into this event.", "viewSchema": "View task schema", "generateWithLLM": "Generate with LLM", "jsonTasks": "JSON Tasks", "uploadJson": "Upload a JSON file containing an array of tasks to import.", "chooseFile": "Choose <PERSON>", "fileContentPreview": "File Content Preview:", "cancel": "Cancel", "import": "Import", "schemaDialogTitle": "Task Schema", "llmPromptDialogTitle": "LLM Prompt for Task Generation", "copyPrompt": "Copy Prompt", "promptCopied": "Prompt copied to clipboard", "failedToCopy": "Failed to copy to clipboard", "copiedToClipboard": "Copied to clipboard", "successImport": "Successfully imported {{importedCount}} of {{totalCount}} tasks.", "failedImport": "Failed to import tasks: {{message}}", "failedExport": "Failed to export tasks: {{message}}", "failedRead": "Failed to read file: {{message}}", "failedSchema": "Failed to get schema: {{message}}", "noEventId": "No event ID provided. Cannot generate LLM prompt.", "failedPrompt": "Failed to generate LLM prompt: {{message}}"}, "defaultTasks": {"title": "Add Default Tasks", "description": "Select default tasks to add to your event. These tasks will be added with their default settings.", "selectAll": "Select All", "deselectAll": "Deselect All", "add": "Add Selected Tasks", "cancel": "Cancel", "noDefaultTasks": "No default tasks available"}, "dependencyGraph": {"title": "Task Dependency Graph", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "fitView": "Fit View", "layoutDirection": "Layout Direction", "leftToRight": "Left to Right", "topToBottom": "Top to Bottom", "deleteSelected": "Delete Selected", "confirmDelete": "Confirm Delete", "deleteConfirmation": "Are you sure you want to delete the selected task(s)?", "cancel": "Cancel", "delete": "Delete", "removeDependency": "Remove Dependency", "removeDependencyConfirmation": "Are you sure you want to remove the dependency from '{{source}}' to '{{target}}'?", "remove": "Remove", "taskDeleted": "Task deleted successfully", "dependencyRemoved": "Dependency removed successfully", "failedToRemoveDependency": "Failed to remove dependency: {{message}}", "failedToDeleteTask": "Failed to delete task: {{message}}", "clickToAddDependency": "Click on another task to create a dependency", "cancelDependency": "Cancel", "selfDependencyError": "A task cannot depend on itself", "existingDependencyError": "This dependency already exists"}, "errors": {"loadFailed": "Failed to load tasks from the database. Please check your connection and try again.", "createFailed": "Failed to create task: {{message}}", "updateFailed": "Failed to update task: {{message}}", "deleteFailed": "Failed to delete task: {{message}}", "addDefaultFailed": "Failed to add default tasks. Please try again."}, "success": {"created": "Task created successfully", "updated": "Task updated successfully", "deleted": "Task deleted successfully"}}, "taskForm": {"name": "Task Name", "type": "Task Type", "selectType": "Select a type", "location": "Location", "locationPlaceholder": "Where will this task take place?", "details": "Details", "detailsPlaceholder": "Enter any additional details about this task", "status": "Status", "cost": "Cost", "paymentStatus": "Payment Status", "paid": "Paid", "notPaid": "Not Paid", "assignees": "Assignees", "assigneesPlaceholder": "Select assignees", "softDeadline": "Soft Deadline", "hardDeadline": "Hard Deadline", "dependencies": "Dependencies", "dependenciesPlaceholder": "Select tasks that must be completed before this one", "parentTask": "Parent Task", "parentTaskPlaceholder": "Select a parent task if this is a subtask", "parentTaskHelp": "If this is a subtask, select its parent task", "childTasks": "Child Tasks", "childTasksPlaceholder": "Select tasks that should be subtasks of this task", "childTasksHelp": "Add tasks that should be subtasks of this task", "createTask": "Create Task", "updateTask": "Update Task", "duration": "Duration (hh:mm:ss)", "end": "End", "editTask": "Edit Task", "createNewTask": "Create New Task", "startTime": "Start Time", "budgetItems": "Budget Items", "errors": {"nameRequired": "Task name is required", "typeRequired": "Task type is required", "eventRequired": "Event is required", "invalidAssignees": "One or more selected assignees are invalid", "fixErrors": "Please fix the errors in the form before submitting", "savingError": "An error occurred while saving the task", "selfReference": "A task cannot be its own parent", "invalidParent": "Selected parent task is invalid", "selfChild": "A task cannot be its own child", "invalidChildren": "One or more selected child tasks are invalid"}}, "taskCard": {"noDescription": "No description provided"}, "taskList": {"task": "Task", "type": "Type", "status": "Status", "startTime": "Start Time", "duration": "Duration", "endTime": "End Time", "assignee": "Assignee", "deadline": "Deadline", "actions": "Actions", "taskDetails": "Task Details", "noAdditionalDetails": "No additional details provided.", "dependencies": "Dependencies:", "noDependencies": "No dependencies", "assignees": "Assignees:", "location": "Location:", "softDeadline": "Soft Deadline:", "hardDeadline": "Hard Deadline:", "editTask": "Edit Task", "deleteTask": "Delete Task", "confirmDeletion": "Confirm Deletion", "errorDeletingTask": "Error Deleting Task", "deleteConfirmation": "Are you sure you want to delete this task?", "close": "Close", "cancel": "Cancel", "delete": "Delete", "taskDeletedSuccess": "Task deleted successfully", "taskUpdateSuccess": "Task updated successfully", "failedToUpdateTask": "Failed to update task: {{message}}", "failedToDeleteTask": "Failed to delete task: {{message}}", "noTasks": "No tasks found", "createOrAdjustFilters": "Create a new task or adjust your filters to see tasks here.", "notSet": "Not set", "invalidDate": "Invalid", "notAvailable": "Not available", "unassigned": "Unassigned", "noDeadline": "No deadline", "more": "+{{count}} more", "unspecified": "Unspecified", "unknownTask": "Unknown Task", "subtask": "subtask", "subtasks": "subtasks", "assignedTo": "Assigned to", "selected": "selected", "selectAll": "Select all tasks", "batchDelete": "<PERSON><PERSON> Delete", "confirmBatchDeletion": "Confirm Batch Deletion", "errorDeletingTasks": "Error Deleting Tasks", "batchDeleteConfirmation": "Are you sure you want to delete {{count}} selected tasks? This action cannot be undone.", "tasksDeletedSuccess": "Successfully deleted {{count}} tasks", "failedToDeleteTasks": "Failed to delete tasks: {{message}}"}, "eventCard": {"noDescription": "No description provided", "daysRemaining": " ({{days}} days remaining)", "pastEvent": " (Past event)", "members": "Members", "details": "Details", "tooltips": {"manageMembers": "Manage event members and invitations", "viewDetails": "View event details"}}, "calendar": {"today": "Today", "month": "Month", "week": "Week", "day": "Day", "agenda": "Agenda", "titleWithEvent": "Calendar: {{eventTitle}}", "allEvents": "All Events", "views": {"monthly": "Monthly View", "daily": "Daily View"}, "errors": {"loadFailed": "Failed to load calendar data"}, "days": {"sun": "Sun", "mon": "Mon", "tue": "<PERSON><PERSON>", "wed": "Wed", "thu": "<PERSON>hu", "fri": "<PERSON><PERSON>", "sat": "Sat"}, "months": {"january": "January", "february": "February", "march": "March", "april": "April", "may": "May", "june": "June", "july": "July", "august": "August", "september": "September", "october": "October", "november": "November", "december": "December"}, "dailyView": {"showAll": "Show All", "showAssigned": "Show Assigned", "unassigned": "Unassigned", "startTime": "Start Time", "softDeadline": "Soft Deadline", "hardDeadline": "Hard Deadline", "location": "Location", "subtasks": "Subtasks", "dragInfo": {"start": "Start: {{time}}", "end": "End: {{time}}", "duration": "Duration: {{duration}}"}, "taskCreatedSuccess": "Task created successfully", "failedToCreateTask": "Failed to create task: {{message}}"}}, "errors": {"required": "This field is required", "invalidEmail": "Invalid email address", "invalidPhone": "Invalid phone number", "networkError": "Network error. Please try again.", "unauthorized": "Unauthorized. Please log in.", "forbidden": "You don't have permission to access this resource.", "notFound": "Resource not found.", "serverError": "Server error. Please try again later.", "languageUpdateFailed": "Failed to update language preference. Please try again.", "authTokenNotFound": "Authentication token not found. Please log in again.", "userInfoNotFound": "User information not found. Please log in again."}, "notifications": {"title": "Notifications", "markAllRead": "Mark all as read", "noNotifications": "No new notifications", "taskAssigned": "You have been assigned a task", "taskUpdated": "A task has been updated", "eventUpdated": "An event has been updated", "invitedAs": "Invited as {{role}}", "invitations": {"invited": "You've been invited to join", "accept": "Accept", "decline": "Decline"}}, "settings": {"title": "Settings", "language": "Language", "languageHelp": "Choose your preferred language for the application", "languageUpdated": "Language updated successfully", "theme": "Theme", "notifications": "Notifications", "account": "Account", "security": "Security", "privacy": "Privacy", "save": "Save Changes"}, "languages": {"en": "English", "zh-TW": "Traditional Chinese"}, "eventMembers": {"title": "Event Members", "add": "Add Member", "invite": "Invite", "remove": "Remove", "manageMembers": "Manage Members", "manageEventMembers": "Manage Event Members", "tabs": {"currentMembers": "Current Members", "invitations": "Invitations", "inviteNewMember": "Invite New Member"}, "status": {"pending": "Pending", "accepted": "Accepted", "declined": "Declined"}, "roles": {"admin": "Admin", "editor": "Editor", "viewer": "Viewer"}, "errors": {"loadUsersFailed": "Failed to load users", "loadInvitationsFailed": "Failed to load invitations", "selectUser": "Please select a user to invite", "sendInvitationFailed": "Failed to send invitation", "cancelInvitationFailed": "Failed to cancel invitation", "acceptInvitationFailed": "Failed to accept invitation", "declineInvitationFailed": "Failed to decline invitation", "removeCollaboratorFailed": "Failed to remove collaborator", "updateRoleFailed": "Failed to update role"}, "success": {"invitationSent": "Invitation sent successfully", "invitationCanceled": "Invitation canceled successfully", "invitationAccepted": "Invitation accepted successfully", "invitationDeclined": "Invitation declined successfully", "collaboratorRemoved": "Collaborator removed successfully", "roleUpdated": "Role updated successfully"}}, "profile": {"title": "Profile", "personalInfo": "Personal Information", "name": "Name", "email": "Email", "phone": "Phone", "password": "Password", "changePassword": "Change Password", "currentPassword": "Current Password", "newPassword": "New Password", "confirmPassword": "Confirm New Password", "updateProfile": "Update Profile", "profileUpdated": "Profile updated successfully"}, "taskTypes": {"venue": "Venue", "catering": "Catering", "photography": "Photography", "decoration": "Decoration", "entertainment": "Entertainment", "logistics": "Logistics", "other": "Other"}, "taskStatus": {"notStarted": "Not Started", "notstarted": "Not Started", "inProgress": "In Progress", "inprogress": "In Progress", "completed": "Completed", "delayed": "Delayed", "cancelled": "Cancelled"}, "guests": {"title": "Guest Management", "addGuest": "Add Guest", "editGuest": "Edit Guest", "addNewGuest": "Add <PERSON> Guest", "importGuests": "Import Guests", "exportGuests": "Export Guests", "searchPlaceholder": "Search guests...", "noGuests": "No guests found", "addNewToStart": "Add a new guest to get started.", "event": "Event", "updateGuest": "Update Guest", "addAttribute": "Add Guest Attribute", "attributeType": "Attribute Type", "attributeValue": "Attribute Value", "dietaryPreference": "Dietary Preference", "confirmDelete": "Confirm Delete", "deleteConfirmation": "Are you sure you want to delete this guest?", "guestAttributes": "Guest Attributes", "noAttributesYet": "No attributes added yet. Click \"Add Attribute\" to add dietary preferences, allergies, or other special requirements.", "loadingGuests": "Loading guests...", "enterTypeDetails": "Enter {{type}} details", "form": {"notes": "Notes"}, "columns": {"name": "Name", "email": "Email", "phone": "Phone", "rsvpStatus": "RSVP Status", "group": "Group", "attributes": "Attributes"}, "rsvpStatus": {"confirmed": "Confirmed", "pending": "Pending", "declined": "Declined"}, "attributeTypes": {"dietary": "Dietary", "allergies": "Allergies", "accessibility": "Accessibility", "seatingPreference": "Seating Preference", "specialNeeds": "Special Needs", "other": "Other"}, "dietaryOptions": {"none": "None", "vegetarian": "Vegetarian", "vegan": "Vegan", "pescatarian": "Pescatarian", "glutenFree": "Gluten-Free", "dairyFree": "Dairy-Free", "nutFree": "Nut-Free", "kosher": "<PERSON><PERSON>", "halal": "<PERSON><PERSON>"}, "errors": {"selectEventFirst": "Please select an event first", "saveFailed": "Error saving guest: {{message}}", "deleteFailed": "Error deleting guest: {{message}}", "noEventSelected": "No event selected. Redirecting to events page."}}, "venue": {"title": "Venue Management", "addVenue": "Add Venue", "editVenue": "Edit Venue", "addNewVenue": "Add New Venue", "editFloorPlan": "Edit Floor Plan", "noVenues": "No venues found", "createFirstVenue": "Create your first venue to get started.", "updateVenue": "Update Venue", "venues": "Venues", "floorPlan": "Floor Plan", "createFloorPlan": "Create Floor Plan", "designFloorPlan": "Design Floor Plan", "noFloorPlanYet": "No Floor Plan Yet", "createFloorPlanDescription": "Create a floor plan to arrange tables and seats for this venue.", "selectVenuePrompt": "Please select a venue from the list to view details and floor plan.", "selectVenueFirst": "Please select a venue first", "invalidVenueSelected": "Invalid venue selected. Please select a valid venue.", "form": {"name": "Name", "address": "Address", "capacity": "Capacity", "notes": "Notes", "description": "Description", "imageUrl": "Image URL", "capacityHelperText": "Required field - must be a positive number"}, "floorPlanEditor": {"elements": "Elements", "table": "Table", "seat": "<PERSON><PERSON>", "wall": "Wall", "stage": "Stage", "entrance": "Entrance", "other": "Other", "uploadBackground": "Upload Background", "removeBackground": "Remove Background", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "undo": "Undo", "redo": "Redo", "save": "Save Floor Plan", "moveToFront": "Move to Front", "moveToBack": "Move to Back", "delete": "Delete Element", "guests": "Guests", "searchGuests": "Search guests...", "assignGuest": "Assign Guest", "assignGuests": "Assign Guests", "removeAssignment": "Remove Assignment", "removeAll": "Remove All", "dragElementsHere": "Drag elements from the left panel to add them to the floor plan", "dragGuestsHere": "Drag guests to assign them to tables or seats", "uploadImage": "Upload Image", "dragElements": "Drag Elements", "dragAndDropElements": "Drag and drop elements onto the canvas", "guestList": "Guest List", "noGuestsFound": "No guests found matching your search.", "selected": "Selected", "changeAssignment": "Change Assignment", "manageGuests": "Manage Guests", "bringToFront": "Bring to Front", "sendToBack": "Send to Back", "deleteElement": "Delete Element", "removeGuestAssignment": "<PERSON><PERSON><PERSON> Guest Assignment", "removeAllGuestAssignments": "Remove All Guest Assignments", "assignGuestToSeat": "Assign Guest to <PERSON>t", "assignGuestsToTable": "Assign Guests to Table", "assigned": "Assigned", "guest": "Guest"}, "errors": {"loadFailed": "Failed to load venues. Please try again later.", "createFailed": "Failed to create venue. Please try again.", "updateFailed": "Failed to update venue. Please try again.", "deleteFailed": "Failed to delete venue. Please try again.", "saveFloorPlanFailed": "Failed to save floor plan. Please try again.", "saveError": "Error saving venue:", "deleteError": "Error deleting venue:", "uploadBackgroundFailed": "Error uploading background image. The floor plan will still work, but the background may not be saved permanently."}}, "stakeholders": {"title": "Stakeholder Management", "addCompany": "Add Company", "editCompany": "Edit Company", "addNewCompany": "Add New Company", "editContact": "Edit Contact", "addNewContact": "Add Contact", "noCompanies": "No companies found", "searchPlaceholder": "Search stakeholders and contacts...", "event": "Event", "contactPoints": "Contact Points", "companyDetails": "Company Details", "noContacts": "No contacts", "noContactsAvailable": "No contact points available", "noContactsAdded": "No contact points added yet", "searchHelp": "Search by company name, contact info, or any contact point details", "deleteConfirmation": "Are you sure you want to delete this stakeholder?", "loadingStakeholders": "Loading stakeholders...", "noStakeholdersFound": "No stakeholders found", "contactsCount": "{{count}} contact", "contactsCountPlural": "{{count}} contacts", "match": "Match", "matchingContactsTooltip": "Contains matching contacts", "expandTooltip": "Expand", "collapseTooltip": "Collapse", "editContactPoint": "Edit Contact Point", "addContactPoint": "Add Contact Point", "columns": {"companyName": "Company Name", "email": "Email", "phone": "Phone", "contactPoints": "Contact Points", "actions": "Actions", "industry": "Industry", "contactInfo": "Contact Info"}, "form": {"companyName": "Company Name", "industry": "Industry", "website": "Website", "email": "Email", "phone": "Phone", "address": "Address", "notes": "Notes", "name": "Name", "position": "Position", "role": "Role"}, "actions": {"edit": "Edit", "delete": "Delete", "create": "Create", "update": "Update", "cancel": "Cancel", "add": "Add"}, "details": {"address": "Address", "website": "Website", "notes": "Notes"}, "success": {"created": "Stakeholder created successfully", "updated": "Stakeholder updated successfully", "deleted": "Stakeholder deleted successfully", "contactAdded": "Contact point added successfully", "contactUpdated": "Contact point updated successfully", "contactDeleted": "Contact point deleted successfully"}, "errors": {"loadFailed": "Failed to load stakeholders", "createFailed": "Failed to create stakeholder", "updateFailed": "Failed to update stakeholder", "deleteFailed": "Failed to delete stakeholder"}}, "features": {"taskTracking": {"title": "Task Tracking", "description": "Organize tasks with deadlines, assign to team members, and track progress in real-time."}, "eventManagement": {"title": "Event Management", "description": "Create and manage multiple events with customizable templates for different event types."}, "header": {"title": "Powerful Features for Successful Events", "subtitle": "Discover all the tools you need to plan and execute flawless events"}, "cta": {"title": "Ready to Start Planning Your Events?", "subtitle": "Join thousands of event planners who trust our platform to deliver successful events.", "signUp": "Sign Up Free", "viewPricing": "View Pricing"}, "sections": {"eventManagement": {"title": "Event Management", "description": "Create and manage multiple events with powerful organization tools.", "feature1": "Create unlimited events with customizable details", "feature2": "Track event progress with visual indicators", "feature3": "Set event dates, locations, and budgets", "feature4": "Organize events by type and category"}, "taskManagement": {"title": "Task Management", "description": "Keep track of all your event tasks with advanced task management.", "feature1": "Assign multiple team members to tasks", "feature2": "Set soft and hard deadlines with reminders", "feature3": "Track task status and progress", "feature4": "Organize tasks by type and priority"}, "guestManagement": {"title": "Guest Management", "description": "Manage your guest list with comprehensive guest tracking features.", "feature1": "Track RSVPs and meal preferences", "feature2": "Organize guests by groups or categories", "feature3": "Assign guests to tables and seats", "feature4": "Send automated guest communications"}, "venuePlanning": {"title": "Venue Planning", "description": "Design and visualize your venue with our interactive floor plan editor.", "feature1": "Drag-and-drop floor plan editor", "feature2": "Create custom table layouts and seating arrangements", "feature3": "Upload venue floor plans as background images", "feature4": "Assign guests directly to tables and seats"}, "calendar": {"title": "Calendar & Scheduling", "description": "Visualize your event timeline with our intuitive calendar views.", "feature1": "Monthly overview of all event tasks", "feature2": "Daily view with detailed task information", "feature3": "Assignee-based task organization", "feature4": "Automatic deadline warnings and notifications"}, "teamCollaboration": {"title": "Team Collaboration", "description": "Work together seamlessly with your event planning team.", "feature1": "Invite team members to collaborate on events", "feature2": "Assign tasks to multiple team members", "feature3": "Track individual contributions and progress", "feature4": "Real-time updates and notifications"}, "templates": {"title": "Templates & Checklists", "description": "Save time with pre-built templates and customizable checklists.", "feature1": "Event templates for different event types", "feature2": "Customizable task checklists", "feature3": "Save your own templates for future use", "feature4": "Import and export templates"}, "analytics": {"title": "Analytics & Reporting", "description": "Gain insights into your event planning with detailed analytics.", "feature1": "Track budget allocation and spending", "feature2": "Monitor task completion rates", "feature3": "Analyze team performance", "feature4": "Generate comprehensive event reports"}, "notifications": {"title": "Notifications & Reminders", "description": "Stay on top of your event planning with timely notifications.", "feature1": "Deadline reminders for tasks", "feature2": "Important milestone notifications", "feature3": "Customizable notification preferences", "feature4": "Email and in-app notifications"}, "crossPlatform": {"title": "Cross-Platform Access", "description": "Access your event planning from anywhere, on any device.", "feature1": "Responsive web application", "feature2": "Mobile-friendly interface", "feature3": "Consistent experience across devices", "feature4": "Offline access to essential information"}, "security": {"title": "Security & Privacy", "description": "Keep your event data secure with our robust security features.", "feature1": "Secure user authentication", "feature2": "Data encryption for sensitive information", "feature3": "Granular permission controls", "feature4": "Regular security updates and monitoring"}}}, "pricing": {"title": "Simple, Transparent Pricing", "subtitle": "Choose the plan that fits your event planning needs", "mostPopular": "MOST POPULAR", "perMonth": "/month", "tiers": {"free": {"title": "Free", "price": "0", "description": "Perfect for small, simple events", "buttonText": "Sign up free"}, "pro": {"title": "Pro", "price": "19.99", "description": "For professional event planners", "buttonText": "Start free trial"}, "enterprise": {"title": "Enterprise", "price": "49.99", "description": "For large organizations with complex events", "buttonText": "Contact sales"}}, "features": {"title": "Compare Features", "subtitle": "Find the plan that includes all the features you need", "feature": "Feature", "events": "Number of Events", "tasks": "Tasks per event", "guests": "Guests per event", "venues": "Venue floor plans", "templates": "Event templates", "team": "Team members", "support": "Support", "export": "Export to PDF/CSV", "api": "API access", "whiteLabel": "White labeling", "dedicated": "Dedicated account manager", "free": {"events": "Up to 3 events", "guests": "Up to 50 guests per event", "taskManagement": "Basic task management", "venuePlanning": "Simple venue planning", "support": "Email support", "taskDependencies": "Advanced task dependencies", "teamCollaboration": "Team collaboration", "templates": "Custom event templates", "prioritySupport": "Priority support"}, "pro": {"events": "Unlimited events", "guests": "Up to 200 guests per event", "taskManagement": "Advanced task management", "venuePlanning": "Detailed venue planning", "support": "Email & chat support", "taskDependencies": "Task dependencies & timelines", "teamCollaboration": "Team collaboration (up to 5 users)", "templates": "Custom event templates", "prioritySupport": "Priority support"}, "enterprise": {"events": "Unlimited events", "guests": "Unlimited guests", "taskManagement": "Premium task management", "venuePlanning": "Advanced venue planning", "support": "Priority support", "taskDependencies": "Advanced task dependencies", "teamCollaboration": "Unlimited team collaboration", "branding": "Custom branding", "api": "API access"}}, "comparison": {"guestsPerEvent": "Guests per Event", "taskManagement": "Task Management", "taskDependencies": "Task Dependencies", "teamCollaboration": "Team Collaboration", "venuePlanning": "Venue Planning", "customTemplates": "Custom Templates", "support": "Support", "apiAccess": "API Access", "unlimited": "Unlimited", "basic": "Basic", "advanced": "Advanced", "premium": "Premium", "free": {"events": "Up to 3", "guests": "Up to 50", "support": "Email"}, "pro": {"guests": "Up to 200", "teamMembers": "Up to 5 users", "support": "Email & Chat"}, "enterprise": {"support": "Priority"}}, "faq": {"title": "Frequently Asked Questions", "subtitle": "Have questions about our pricing? Find answers below", "questions": {"q1": {"question": "Can I upgrade or downgrade my plan at any time?", "answer": "Yes, you can change your plan at any time. When upgrading, you'll be charged the prorated amount for the remainder of your billing cycle. When downgrading, the new rate will apply at the start of your next billing cycle."}, "q2": {"question": "Is there a free trial?", "answer": "Yes! You can use the Free plan indefinitely with its limitations, or try the Pro plan free for 14 days. No credit card required for the trial."}, "q3": {"question": "What payment methods do you accept?", "answer": "We accept all major credit cards, including Visa, Mastercard, American Express, and Discover. For Enterprise plans, we also offer invoicing options."}, "q4": {"question": "Do you offer discounts for non-profits?", "answer": "Yes, we offer special pricing for non-profit organizations. Please contact our sales team for more information and to verify your non-profit status."}, "q5": {"question": "Is my data secure?", "answer": "Yes, we take security seriously. All data is encrypted in transit and at rest. We use industry-standard security practices and regularly undergo security audits."}}}, "finalCta": {"title": "Ready to Get Started?", "subtitle": "Choose the plan that's right for you and start planning amazing events today.", "signUp": "Sign Up Now", "contactSales": "Contact Sales"}}, "landing": {"hero": {"title": "Plan Perfect Events, Every Time", "subtitle": "The all-in-one event planning platform that helps you manage tasks, guests, venues, and schedules with ease.", "getStarted": "Get Started Free", "learnMore": "Learn More", "viewPricing": "View Pricing"}, "features": {"title": "Everything You Need to Plan Successful Events", "subtitle": "Our comprehensive platform streamlines every aspect of event planning, from task management to guest coordination.", "eventManagement": {"title": "Event Management", "description": "Create and manage multiple events with customizable templates for different event types."}, "taskManagement": {"title": "Task Management", "description": "Create, assign, and track tasks with deadlines and dependencies."}, "guestManagement": {"title": "Guest Management", "description": "Manage invitations, RSVPs, seating arrangements, and dietary preferences."}, "venueManagement": {"title": "Venue Planning", "description": "Design floor plans, arrange tables, and visualize your event space."}, "teamCollaboration": {"title": "Team Collaboration", "description": "Work together seamlessly with your entire event planning team."}, "taskTracking": {"title": "Task Tracking", "description": "Organize tasks with deadlines, assign to team members, and track progress in real-time."}, "venuePlanning": {"title": "Venue Planning", "description": "Design floor plans, arrange seating, and visualize your venue setup with our drag-and-drop editor."}}, "testimonials": {"title": "What Our Users Say", "subtitle": "Join thousands of satisfied event planners who trust our platform.", "testimonial1": {"quote": "This platform has transformed how we plan corporate events. Everything is organized in one place, and our team can collaborate effortlessly.", "author": "<PERSON>, Corporate Event Manager"}, "testimonial2": {"quote": "As a wedding planner, I need to keep track of countless details. This tool has been a lifesaver for managing all aspects of my clients' special days.", "author": "<PERSON>, Wedding Planner"}, "testimonial3": {"quote": "The venue planning feature is incredible. Being able to visualize the space and arrange seating has made our events much more successful.", "author": "<PERSON>, Conference Organizer"}}, "cta": {"title": "Ready to Simplify Your Event Planning?", "subtitle": "Join thousands of event planners who trust our platform to deliver successful events.", "button": "Start Planning Today"}, "featuresList": {"title": "Powerful Features for Every Event", "subtitle": "Explore all the tools you need to plan and execute perfect events", "eventManagement": {"title": "Comprehensive Event Management", "feature1": "Multiple event types - Plan weddings, corporate events, parties and more with dedicated templates", "feature2": "Progress tracking - Monitor task completion with visual progress bars", "feature3": "Budget management - Track expenses and stay within budget"}, "taskManagement": {"title": "Intuitive Task Management", "feature1": "Multiple assignees - Assign tasks to multiple team members", "feature2": "Deadline tracking - Set soft and hard deadlines with automatic reminders", "feature3": "Daily view - See tasks organized by assignee for efficient workload management"}, "guestManagement": {"title": "Guest Management", "feature1": "RSVP tracking - Monitor guest responses and meal preferences", "feature2": "Guest categorization - Organize guests by groups or relationships", "feature3": "Table assignments - Easily assign guests to tables and seats"}, "venuePlanning": {"title": "Venue Floor Planning", "feature1": "Drag-and-drop editor - Design floor plans with intuitive controls", "feature2": "Custom elements - Add tables, chairs, stages, and other venue elements", "feature3": "Background images - Upload venue floor plans as reference backgrounds"}}, "finalCta": {"title": "Start Planning Your Perfect Event Now", "signUp": "Sign Up Free", "viewPricing": "View Pricing"}}, "resources": {"title": "Resources"}, "budget": {"title": "Budget Management", "forEvent": "For event: {{eventTitle}}", "summary": "Budget Summary", "totalEstimated": "Total Estimated", "totalActual": "Total Actual", "totalPaid": "Total Paid", "totalUnpaid": "Total Unpaid", "variance": "<PERSON><PERSON><PERSON>", "task": "Task", "description": "Description", "estimatedAmount": "Estimated Amount", "actualAmount": "Actual Amount", "category": "Category", "paymentStatus": "Payment Status", "vendor": "<PERSON><PERSON><PERSON>", "currency": "<PERSON><PERSON><PERSON><PERSON>", "notes": "Notes", "paid": "Paid", "notPaid": "Not Paid", "addBudgetItem": "Add Budget Item", "editBudgetItem": "Edit Budget Item", "noBudgetItems": "No budget items found", "addBudgetItemsPrompt": "Add budget items to track your expenses", "estimatedTotal": "Estimated Total", "actualTotal": "Actual Total", "addItem": "Add Item", "noItemsForTask": "No budget items for this task", "categoryBreakdown": "Category Breakdown", "searchPlaceholder": "Search budget items...", "filterByCategory": "Filter by Category", "hideTasksWithNoCost": "Hide tasks with no cost items", "status": "Status", "estimated": "Estimated", "actual": "Actual", "allocated": "Allocated Budget", "overAllocated": "Over-allocated by {{amount}}", "unallocated": "Unallocated: {{amount}}", "allocatedTooltip": "{{percent}}% of total budget allocated", "actualTooltip": "{{percent}}% of total budget spent", "overBudget": "Over budget by {{amount}}", "underBudget": "Under budget by {{amount}}", "overBudgetWarning": "Over budget by {{amount}}", "unspentAllocated": "Unspent allocated: {{amount}}", "views": {"byTask": "By Task", "byCategory": "By Category"}, "categories": {"venue": "Venue", "catering": "Catering", "decoration": "Decoration", "entertainment": "Entertainment", "photography": "Photography", "transportation": "Transportation", "accommodation": "Accommodation", "marketing": "Marketing", "gifts": "Gifts", "printing": "Printing", "staff": "Staff", "equipment": "Equipment", "other": "Other"}, "success": {"added": "Budget item added successfully", "updated": "Budget item updated successfully", "deleted": "Budget item deleted successfully"}, "errors": {"loadFailed": "Failed to load budget data", "addFailed": "Failed to add budget item", "updateFailed": "Failed to update budget item", "deleteFailed": "Failed to delete budget item"}}, "company": {"name": "EKHM Limited", "allRightsReserved": "All rights reserved.", "about": "About {{companyName}}", "ourCompany": "Our Company", "description": "{{companyName}} provides comprehensive event planning software that streamlines the organization of events of all sizes. Our platform offers intuitive tools for event management, task tracking, guest management, and venue planning, all designed to make your event planning process effortless.", "termsIntro": "By using {{companyName}}'s services, you agree to the following terms:", "termsReserveRight": "{{companyName}} reserves the right to update services, features, and pricing at its discretion.", "termsUnauthorized": "Unauthorized reproduction, distribution, or resale of {{companyName}} products or services is prohibited.", "termsIntellectualProperty": "All intellectual property rights in the Event Planner App and other services remain the property of {{companyName}}.", "services": {"title": "Our Services", "eventManagement": {"title": "Event Management", "description": "Comprehensive tools for organizing, tracking, and executing events from start to finish. Create custom event timelines, manage resources, and coordinate with your team in real-time."}, "taskTracking": {"title": "Task Tracking", "description": "Intuitive task management system with deadlines, assignments, and progress tracking. Break down complex events into manageable tasks and ensure nothing gets overlooked."}, "guestManagement": {"title": "Guest Management", "description": "Effortlessly manage invitations, RSVPs, and guest preferences. Track dietary requirements, seating arrangements, and special accommodations all in one place."}, "venuePlanning": {"title": "Venue Planning", "description": "Visualize and organize venue layouts, manage vendor relationships, and coordinate logistics. Compare venues, track availability, and manage contracts seamlessly."}}, "contactUs": {"title": "Contact Us", "description": "We're here to help with any questions or concerns about our services.", "email": "Email: <EMAIL>", "businessHours": "Business Hours: Monday to Friday, 9:00 AM - 5:00 PM GMT"}, "refundPolicy": {"title": "Refund and Cancellation Policies", "subscriptions": "Subscriptions:", "subscriptionsDetails": "You may cancel your subscription at any time. Cancellations will be effective at the end of the current billing cycle.", "refunds": "Refunds:", "refundsDetails": "We offer no refunds for partial subscription periods or unused features. Refunds are only provided if a system error causes service interruption exceeding 72 continuous hours.", "freeTier": "Free Tier:", "freeTierDetails": "The Free Tier may be canceled at any time without penalty."}, "terms": {"autoRenew": "Subscriptions automatically renew unless canceled prior to the end of the current term."}, "privacy": {"intro": "We are committed to protecting your privacy. This policy outlines how we collect, use, and protect your information:", "dataCollection": "Data Collection:", "dataCollectionDetails": "We collect information you provide directly (e.g., when creating an account) and automatically through your use of our services.", "useOfInfo": "Use of Information:", "useOfInfoDetails": "Your information is used to deliver services, process transactions, provide customer support, and improve our offerings.", "dataSharing": "Data Sharing:", "dataSharingDetails": "We do not sell your personal information. We may share data with trusted service providers as necessary to operate our services.", "security": "Security:", "securityDetails": "We implement strong security measures to protect your personal information.", "userRights": "User Rights:", "userRightsDetails": "You have the right to access, correct, or delete your personal information by contacting our support team."}}, "supplies": {"title": "Wedding Supplies", "name": "Name", "description": "Description", "quantity": "Quantity", "unit": "Unit", "estimatedCost": "Estimated Cost", "actualCost": "Actual Cost", "vendor": "<PERSON><PERSON><PERSON>", "category": "Category", "status": "Status", "isPurchased": "Purchased", "notPurchased": "Not Purchased", "purchased": "Purchased", "purchaseDate": "Purchase Date", "notes": "Notes", "addSupply": "Add Supply", "editSupply": "Edit Supply", "createNewSupply": "Create New Supply", "addExistingSupply": "Add Existing Supply", "noSupplies": "No supplies added yet", "addFirstSupply": "Add Your First Supply", "alreadyAdded": "Added", "noSuppliesAvailable": "No supplies available", "summary": "Summary", "totalItems": "Total Items", "purchasedItems": "Purchased Items", "totalEstimatedCost": "Total Estimated Cost", "totalActualCost": "Total Actual Cost", "associatedTasks": "Associated Tasks", "confirmDelete": "Confirm Delete", "deleteConfirmation": "Are you sure you want to delete this supply? This action cannot be undone.", "noEventSelected": "Please select an event to view supplies", "fetchError": "Failed to load supplies. Please try again.", "createError": "Failed to create supply. Please try again.", "updateError": "Failed to update supply. Please try again.", "deleteError": "Failed to delete supply. Please try again.", "categories": {"decoration": "Decoration", "food": "Food", "beverage": "Beverage", "tableware": "Tableware", "furniture": "Furniture", "stationery": "Stationery", "clothing": "Clothing", "electronics": "Electronics", "other": "Other"}, "units": {"piece": "Piece", "set": "Set", "box": "Box", "pack": "Pack", "kg": "kg", "g": "g", "l": "L", "ml": "mL", "dozen": "<PERSON><PERSON>", "pair": "Pair", "meter": "<PERSON>er", "cm": "cm"}}}