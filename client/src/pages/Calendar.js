import React, { useState, useEffect, useContext } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Typography,
  Tabs,
  Tab,
  Paper,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import MonthlyView from '../components/Calendar/MonthlyView';
import DailyView from '../components/Calendar/DailyView';
import { fetchTasksForCalendar, createTask, updateTask } from '../services/taskService';
import { fetchUsers } from '../services/userService';
import { EventContext } from '../contexts/EventContext';

const Calendar = () => {
  const { selectedEventId, currentEvent, setSelectedEventId } = useContext(EventContext);
  const { view: urlView, date: urlDate } = useParams();
  const navigate = useNavigate();
  const location = useLocation();
  const { t } = useTranslation();

  // Initialize view based on URL parameter if available, otherwise default to monthly
  const [view, setView] = useState(urlView || 'monthly');

  // Extract eventId from URL if present (for /calendar/:eventId/:view format)
  const eventIdFromPath = location.pathname.split('/').length > 2 ?
    location.pathname.split('/')[2] : null;

  // Use eventId from URL if it's a valid ID format, otherwise use selectedEventId from context
  const effectiveEventId = /^[0-9a-fA-F]{24}$/.test(eventIdFromPath) ?
    eventIdFromPath : selectedEventId;

  // Parse date from URL if available, otherwise use current date
  const parseDateFromUrl = (dateString) => {
    if (!dateString) return new Date();
    try {
      const date = new Date(dateString);
      return isNaN(date.getTime()) ? new Date() : date;
    } catch (e) {
      console.error('Error parsing date from URL:', e);
      return new Date();
    }
  };

  const [selectedDate, setSelectedDate] = useState(parseDateFromUrl(urlDate));
  const [events, setEvents] = useState([]);
  const [tasks, setTasks] = useState([]);
  const [users, setUsers] = useState([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [snackbarOpen, setSnackbarOpen] = useState(false);
  const [snackbarMessage, setSnackbarMessage] = useState('');
  const [snackbarSeverity, setSnackbarSeverity] = useState('success');

  // Update view when URL parameter changes
  useEffect(() => {
    if (urlView && urlView !== view) {
      setView(urlView);
    }

    // Handle special case for /calendar/monthly or /calendar/daily
    // If the URL is in format /calendar/monthly, treat 'monthly' as view, not as eventId
    if (urlView === 'monthly' || urlView === 'daily') {
      setView(urlView);
    }
    console.log('Calendar - urlView changed:', urlView);
  }, [urlView, view]);

  // Update selectedDate when URL date parameter changes
  useEffect(() => {
    if (urlDate) {
      const parsedDate = parseDateFromUrl(urlDate);
      console.log('Calendar - URL date changed:', urlDate, 'parsed as:', parsedDate);
      setSelectedDate(parsedDate);
    }
  }, [urlDate]);

  // Log when selectedDate changes
  useEffect(() => {
    console.log('Calendar - selectedDate changed:', selectedDate);
  }, [selectedDate]);

  useEffect(() => {
    const fetchData = async () => {
      // Allow calendar to work without an event ID
      if (!effectiveEventId && !selectedEventId) {
        setLoading(false);
        return;
      }

      setLoading(true);
      try {
        // For the calendar, we only need the current event
        setEvents(currentEvent ? [currentEvent] : []);

        // Calculate date range - fetch a wider range to support navigation
        const today = new Date();
        // Fetch 6 months before and 6 months after current date to support navigation
        const startDate = new Date(today.getFullYear(), today.getMonth() - 6, 1);
        const endDate = new Date(today.getFullYear(), today.getMonth() + 7, 0);

        // Fetch tasks from the backend using our new API endpoint
        console.log('Fetching calendar tasks from backend for event:', effectiveEventId);
        const eventTasks = await fetchTasksForCalendar({
          startDate: startDate.toISOString(),
          endDate: endDate.toISOString(),
          eventId: effectiveEventId
        });

        console.log('Received calendar tasks:', eventTasks);
        setTasks(eventTasks);

        // Fetch all users from the database
        try {
          console.log('Fetching all users from database');
          const allUsers = await fetchUsers();
          console.log('Received users:', allUsers);
          setUsers(allUsers);
        } catch (userErr) {
          console.error('Error fetching users:', userErr);
          // If we can't fetch all users, fall back to event collaborators
          setUsers(currentEvent?.collaborators || []);
        }
      } catch (err) {
        console.error('Error fetching calendar data:', err);
        setError(t('calendar.errors.loadFailed', 'Failed to load calendar data'));
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [effectiveEventId, currentEvent]);

  const handleViewChange = (event, newValue) => {
    setView(newValue);
    // Update URL when view changes, preserving the date parameter if it exists and we're going to daily view
    if (newValue === 'daily' && selectedDate) {
      const formattedDate = selectedDate.toISOString().split('T')[0];
      navigate(`/calendar/${newValue}/${formattedDate}`);
    } else {
      navigate(`/calendar/${newValue}`);
    }
  };

  const handleDateSelect = (date) => {
    console.log('Date selected:', date);
    // Create a new date object to ensure we're working with the exact date
    const newDate = new Date(date);
    console.log('New date object created:', newDate);

    // Set the selected date and update the view
    setSelectedDate(newDate);
    setView('daily');

    // Format the date for the URL (YYYY-MM-DD)
    const formattedDate = newDate.toISOString().split('T')[0];
    console.log('Formatted date for URL:', formattedDate);

    // Update URL when switching to daily view with a selected date
    navigate(`/calendar/daily/${formattedDate}`);
  };

  // Handle task creation
  const handleTaskCreate = async (taskData) => {
    try {
      console.log('Creating new task:', taskData);

      // Ensure the event ID is set
      if (!taskData.event && effectiveEventId) {
        taskData.event = effectiveEventId;
      }

      // Call createTask with both eventId and taskData parameters
      // The first parameter should be the eventId, the second should be the taskData
      const newTask = await createTask(effectiveEventId, taskData);
      console.log('Task created successfully:', newTask);

      // Update the local tasks state immediately to show the new task on the calendar
      setTasks(prevTasks => {
        console.log('Adding new task to calendar view:', newTask);
        return [...prevTasks, newTask];
      });

      // Show success message
      setSnackbarMessage('Task created successfully');
      setSnackbarSeverity('success');
      setSnackbarOpen(true);

      return newTask;
    } catch (error) {
      console.error('Error creating task:', error);
      setError('Failed to create task');

      // Show error message
      setSnackbarMessage(`Failed to create task: ${error.message || 'Unknown error'}`);
      setSnackbarSeverity('error');
      setSnackbarOpen(true);

      throw error;
    }
  };

  // Handle task update
  const handleTaskUpdate = async (taskData) => {
    try {
      console.log('Updating task with ID:', taskData._id);
      console.log('Update payload:', taskData);

      // Ensure we have a task ID
      if (!taskData._id) {
        throw new Error('Task ID is required for updates');
      }

      // Call the updateTask function with the task ID and data
      const updatedTask = await updateTask(taskData._id, taskData);
      console.log('Task updated successfully:', updatedTask);

      // Update the local tasks state
      setTasks(prevTasks =>
        prevTasks.map(task =>
          task._id === updatedTask._id ? updatedTask : task
        )
      );

      return updatedTask;
    } catch (error) {
      console.error('Error updating task:', error);
      setError('Failed to update task');
      throw error;
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      <Typography variant="h4" gutterBottom>
        {t('calendar.titleWithEvent', 'Calendar: {{eventTitle}}', { eventTitle: currentEvent?.title || t('calendar.allEvents', 'All Events') })}
      </Typography>

      <Paper sx={{ mb: 3 }}>
        <Tabs
          value={view}
          onChange={handleViewChange}
          indicatorColor="primary"
          textColor="primary"
          centered
        >
          <Tab value="monthly" label={t('calendar.views.monthly', 'Monthly View')} />
          <Tab value="daily" label={t('calendar.views.daily', 'Daily View')} />
        </Tabs>
      </Paper>

      {view === 'monthly' ? (
        <MonthlyView
          events={events}
          tasks={tasks}
          onDateSelect={handleDateSelect}
        />
      ) : (
        <DailyView
          selectedDate={selectedDate}
          events={events}
          tasks={tasks}
          users={users}
          onTaskCreate={handleTaskCreate}
          onTaskUpdate={handleTaskUpdate}
        />
      )}

      {/* Success/Error Snackbar */}
      <Snackbar
        open={snackbarOpen}
        autoHideDuration={6000}
        onClose={() => setSnackbarOpen(false)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={() => setSnackbarOpen(false)}
          severity={snackbarSeverity}
          variant="filled"
          sx={{ width: '100%' }}
        >
          {snackbarMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default Calendar;